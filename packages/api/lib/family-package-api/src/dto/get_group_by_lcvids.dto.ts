import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

export class GroupPersonJunctionDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  groupId: string;

  @ApiProperty()
  @Expose()
  personId: string;

  @ApiProperty()
  @Expose()
  lcvId: string;

  @ApiProperty()
  @Expose()
  status: number;

  @ApiProperty()
  @Expose()
  isVerified: boolean;

  @ApiProperty()
  @Expose()
  createdTime: string;

  @ApiProperty()
  @Expose()
  createdBy: string;

  @ApiProperty()
  @Expose()
  modifiedTime: string;

  @ApiProperty({ nullable: true })
  @Expose()
  modifiedBy: string | null;
}

export class EvidenceDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  evidenceTypeId: string;

  @ApiProperty()
  @Expose()
  groupId: string;

  @ApiProperty()
  @Expose()
  evidenceData: string;

  @ApiProperty()
  @Expose()
  personId: string;

  @ApiProperty({ nullable: true })
  @Expose()
  evidenceDate: string | null;

  @ApiProperty()
  @Expose()
  evidenceType: number;

  @ApiProperty()
  @Expose()
  createdTime: string;

  @ApiProperty()
  @Expose()
  createdBy: string;

  @ApiProperty()
  @Expose()
  modifiedTime: string;

  @ApiProperty({ nullable: true })
  @Expose()
  modifiedBy: string | null;
}

export class GroupDetailDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  groupName: string;

  @ApiProperty()
  @Expose()
  groupTypeId: string;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty()
  @Expose()
  groupRankingId: string;

  @ApiProperty()
  @Expose()
  level: number;

  @ApiProperty()
  @Expose()
  groupRankingName: string;

  @ApiProperty()
  @Expose()
  groupRankingActivateDate: string;

  @ApiProperty()
  @Expose()
  groupRankingDeactivateDate: string;

  @ApiProperty()
  @Expose()
  owner: string;

  @ApiProperty()
  @Expose()
  status: number;

  @ApiProperty()
  @Expose()
  shopCode: string;

  @ApiProperty()
  @Expose()
  createdTime: string;

  @ApiProperty()
  @Expose()
  createdBy: string;

  @ApiProperty()
  @Expose()
  modifiedTime: string;

  @ApiProperty({ nullable: true })
  @Expose()
  modifiedBy: string | null;

  @ApiProperty({ type: [GroupPersonJunctionDto] })
  @Expose()
  @Type(() => GroupPersonJunctionDto)
  groupPersonJunctions: GroupPersonJunctionDto[];

  @ApiProperty({ type: [EvidenceDto] })
  @Expose()
  @Type(() => EvidenceDto)
  evidence: EvidenceDto[];

  @ApiProperty()
  @Expose()
  groupRankingEnglishName: string;
}
