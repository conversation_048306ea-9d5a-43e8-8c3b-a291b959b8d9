import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

// <PERSON><PERSON><PERSON><PERSON> kiện xếp hạng nhóm
export class GroupRankingConditionDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  groupTypeId: string;

  @ApiProperty()
  @Expose()
  groupRankingId: string;

  @ApiProperty()
  @Expose()
  attributeCode: string;

  @ApiProperty()
  @Expose()
  valueFrom: number;

  @ApiProperty()
  @Expose()
  valueTo: number;
}

// Mô tả loại nhóm theo hạng
export class GroupTypeDescriptionDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  groupTypeId: string;

  @ApiProperty()
  @Expose()
  groupRankingId: string;

  @ApiProperty()
  @Expose()
  condition: string;

  @ApiProperty()
  @Expose()
  shortCondition: string;

  @ApiProperty()
  @Expose()
  promotion: string;

  @ApiProperty()
  @Expose()
  iconLink: string;

  @ApiProperty()
  @Expose()
  iconLinkSecondary: string;

  @ApiProperty()
  @Expose()
  iconTagLink: string;

  @ApiProperty()
  @Expose()
  rankNameDisplay: string;
}

// Hạng nhóm
export class GroupRankingDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  groupRankingName: string;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty()
  @Expose()
  effectiveDurationValue: number;

  @ApiProperty()
  @Expose()
  effectiveDurationUnit: number;

  @ApiProperty()
  @Expose()
  level: number;

  @ApiProperty({ type: [GroupRankingConditionDto] })
  @Type(() => GroupRankingConditionDto)
  @Expose()
  groupRankingConditions: GroupRankingConditionDto[];

  @ApiProperty({ type: GroupTypeDescriptionDto })
  @Type(() => GroupTypeDescriptionDto)
  @Expose()
  groupTypeDescription: GroupTypeDescriptionDto;

  @ApiProperty({ nullable: true })
  @Expose()
  groupRankingEnglishName: string | null;
}

// Quy tắc nhóm
export class GroupRuleDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  quantityPersonFrom: number;

  @ApiProperty()
  @Expose()
  quantityPersonTo: number;
}

// Chiến dịch
export class CampaignDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  campaignName: string;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty({ nullable: true })
  @Expose()
  content: string | null;

  @ApiProperty()
  @Expose()
  startDate: string;

  @ApiProperty()
  @Expose()
  endDate: string;
}

// DTO chính - Chi tiết loại nhóm
export class GroupTypeDetailDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  groupTypeCode: number;

  @ApiProperty()
  @Expose()
  groupTypeName: string;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty({ type: CampaignDto })
  @Type(() => CampaignDto)
  @Expose()
  campaign: CampaignDto;

  @ApiProperty({ type: GroupRuleDto })
  @Type(() => GroupRuleDto)
  @Expose()
  groupRule: GroupRuleDto;

  @ApiProperty({ type: [GroupRankingDto] })
  @Type(() => GroupRankingDto)
  @Expose()
  groupRanking: GroupRankingDto[];
}
