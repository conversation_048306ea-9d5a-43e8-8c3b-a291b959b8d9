import { HttpClientRequestConfig } from '../../http-client/src';
import { RequestScheduleDto, RequestScheduleResponseDto } from './dto/schedules/request-schedule.dto';

export abstract class BffOnlineIntegrationApiAbstract {
  //#region Schedule requests
  abstract scheduleRequestOrderOnline(
    payload: RequestScheduleDto,
    configs?: HttpClientRequestConfig,
  ): Promise<RequestScheduleResponseDto>;
  //#endregion Schedule requests
}
