import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';
import { IRequestSchedule, IRequestScheduleSubjectInjection } from '../../interface/schedules';

export class RequestScheduleDto {
  @ApiProperty()
  @Expose()
  @IsString()
  @IsNotEmpty()
  orderCode: string;
}

export class RequestScheduleResponseDto implements IRequestSchedule {
  @ApiProperty()
  @Expose()
  paymentMethod: number;

  @ApiProperty()
  @Expose()
  phoneOfSubjectsInjection: string;

  @ApiProperty()
  @Expose()
  orderCode: string;

  @ApiProperty()
  @Expose()
  customerId: string;

  @ApiProperty()
  @Expose()
  customerName: string;

  @ApiProperty()
  @Expose()
  dateOfBirth: string;

  @ApiProperty()
  @Expose()
  gender: number;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @ApiProperty()
  @Expose()
  personId: string;

  @ApiProperty()
  @Expose()
  type: number;

  @ApiProperty()
  @Expose()
  customerType: number;

  @ApiProperty()
  @Expose()
  orderChannel: number;

  @ApiProperty()
  @Expose()
  subjectInjections: IRequestScheduleSubjectInjection[];

  @ApiProperty()
  @Expose()
  note: string;
  customerNote: string;

  @ApiProperty()
  @Expose()
  sourceId: number;

  @ApiProperty()
  @Expose()
  status: number;

  @ApiProperty()
  @Expose()
  needAdvice: boolean;

  @ApiProperty()
  @Expose()
  updatedBy: string | null;

  @ApiProperty()
  @Expose()
  createdBy: string | null;

  @ApiProperty()
  @Expose()
  createdDate: string | null;

  @ApiProperty()
  @Expose()
  updatedDate: string | null;

  @ApiProperty()
  @Expose()
  landingType: number;

  @ApiProperty()
  @Expose()
  isPreOrder: boolean;

  @ApiProperty()
  @Expose()
  isOutOfQuota: boolean;

  @ApiProperty()
  @Expose()
  injections: [] | null;

  @ApiProperty()
  @Expose()
  disease: [] | null;

  @ApiProperty()
  @Expose()
  distances: string;

  @ApiProperty()
  @Expose()
  email: string;

  @ApiProperty()
  @Expose()
  nameOfSubjectsInjection: string;

  @ApiProperty()
  @Expose()
  unsignedName: string;

  @ApiProperty()
  @Expose()
  dateOfBirthOfSubjectsInjection: string;

  @ApiProperty()
  @Expose()
  addressOfSubjectsInjection: string;

  @ApiProperty()
  @Expose()
  depositAmount: number;

  @ApiProperty()
  @Expose()
  emailOfSubjectsInjection: string;

  @ApiProperty()
  @Expose()
  genderOfSubjectsInjection: number;

  @ApiProperty()
  @Expose()
  shopCode: string;

  @ApiProperty()
  @Expose()
  shopName: string;

  @ApiProperty()
  @Expose()
  shopAddress: string;

  @ApiProperty()
  @Expose()
  calendarType: number;

  @ApiProperty()
  @Expose()
  packageId: string;

  @ApiProperty()
  @Expose()
  packageName: string;

  @ApiProperty()
  @Expose()
  sku: string;

  @ApiProperty()
  @Expose()
  skuName: string;

  @ApiProperty()
  @Expose()
  taxonomies: string;

  @ApiProperty()
  @Expose()
  origin: string;

  @ApiProperty()
  @Expose()
  regimenID: string;

  @ApiProperty()
  @Expose()
  vacOrderId: string;

  @ApiProperty()
  @Expose()
  receptionistCode: string;

  @ApiProperty()
  @Expose()
  receptionistName: string;

  @ApiProperty()
  @Expose()
  appointmentDate: string;

  @ApiProperty()
  @Expose()
  cancelDate: string;

  @ApiProperty()
  @Expose()
  cancelBy: string;

  @ApiProperty()
  @Expose()
  insideCode: string;

  @ApiProperty()
  @Expose()
  insideName: string;

  @ApiProperty()
  @Expose()
  assignedDate: string;

  @ApiProperty()
  @Expose()
  vaccinationTime: string;

  @ApiProperty()
  @Expose()
  isBloodTest: boolean;

  @ApiProperty()
  @Expose()
  campaignType: number;

  @ApiProperty()
  @Expose()
  idLink: string;

  @ApiProperty()
  @Expose()
  partner: string;

  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  uuid: string;

  @ApiProperty()
  @Expose()
  customerSupport: any;
}
