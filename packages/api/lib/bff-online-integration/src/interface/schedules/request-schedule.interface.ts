export interface IRequestScheduleItem {
  itemCode: string;
  itemName: string;
  unitCode: number;
  unitName: string;
  quantity: string;
  price: string;
  priceAfterDiscount: string;
}

export interface IRequestScheduleSubjectInjection {
  nameOfSubjectsInjection: string;
  phoneOfSubjectsInjection: string;
  genderOfSubjectsInjection: number;
  dateOfBirthOfSubjectsInjection: string;
  items: IRequestScheduleItem[];
}

export type IRequestSchedule = {
  orderCode: string;
  customerId: string;
  customerName: string;
  dateOfBirth: string;
  gender: number;
  phoneNumber: string;
  personId: string;
  type: number;
  customerType: number;
  orderChannel: number;
  subjectInjections: IRequestScheduleSubjectInjection[];
  note: string;
  customerNote: string;
  sourceId: number;
  status: number;
  needAdvice: boolean;
  updatedBy: string | null;
  createdBy: string | null;
  createdDate: string | null;
  updatedDate: string | null;
  landingType: number;
  isPreOrder: boolean;
  isOutOfQuota: boolean;
  injections: [] | null;
  disease: [] | null;
  distances: string;
  email: string;
  nameOfSubjectsInjection: string;
  unsignedName: string;
  dateOfBirthOfSubjectsInjection: string;
  addressOfSubjectsInjection: string;
  depositAmount: number;
  paymentMethod: number;
  emailOfSubjectsInjection: string;
  phoneOfSubjectsInjection: string;
  genderOfSubjectsInjection: number;
  shopCode: string;
  shopName: string;
  shopAddress: string;
  calendarType: number;
  packageId: string;
  packageName: string;
  sku: string;
  skuName: string;
  taxonomies: string;
  origin: string;
  regimenID: string;
  vacOrderId: string;
  receptionistCode: string;
  receptionistName: string;
  appointmentDate: string;
  cancelDate: string;
  cancelBy: string;
  insideCode: string;
  insideName: string;
  assignedDate: string;
  vaccinationTime: string;
  isBloodTest: boolean;
  campaignType: number;
  idLink: string;
  partner: string;
  id: string;
  uuid: string;
};
