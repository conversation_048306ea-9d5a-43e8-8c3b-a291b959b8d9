import { Injectable } from '@nestjs/common';
import { _checkExceptionFromAxiosRes } from '../../functions';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import { BffOnlineIntegrationApiAbstract } from './bff-online-integration.abstract';
import { BffOnlineApiEndpoint } from './bff-online-integration.endpoint';
import { RequestScheduleDto, RequestScheduleResponseDto } from './dto/schedules/request-schedule.dto';

@Injectable()
export class BffOnlineIntegrationApiService extends BffOnlineIntegrationApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  //#region Schedule requests
  async scheduleRequestOrderOnline(
    payload: RequestScheduleDto,
    configs?: HttpClientRequestConfig,
  ): Promise<RequestScheduleResponseDto> {
    const axiosRes = await this.httpClientService.post(BffOnlineApiEndpoint.SCHEDULES_REQUEST, payload, {
      ...configs,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data?.data || null;
  }
  //#endregion Schedule requests
}
