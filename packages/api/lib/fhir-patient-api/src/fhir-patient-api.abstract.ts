import { Injectable } from '@nestjs/common';
import { HttpClientRequestConfig } from '../../http-client/src';
import {
  JsonNode,
  ApiResponseJsonNode,
  GetHistoryRequestDto,
  PatientSearchParams,
  PatientSearchAdvancedParams,
  FhirPatientCreatedDto,
  FhirRelatedPersonDto,
  FhirGroupPatientCreatedDto,
  FhirAuditDto,
} from './dto';

/**
 * Abstract class defining FHIR Patient API operations
 * All FHIR Patient service implementations must extend this class
 */
@Injectable()
export abstract class FhirPatientApiAbstract {
  /**
   * Creates a new FHIR Patient resource
   * @param data - The Patient resource data to create
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<ApiResponseJsonNode> - Creation result
   */
  abstract createPatient(
    data: FhirPatientCreatedDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<FhirPatientCreatedDto>;

  /**
   * Updates an existing FHIR Patient resource
   * @param data - The Patient resource data to update
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<ApiResponseJsonNode> - Update result
   */
  abstract updatePatient(
    data: FhirPatientCreatedDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<FhirPatientCreatedDto>;

  /**
   * Validates a FHIR Patient resource
   * @param data - The Patient resource data to validate
   * @param configs - Optional HTTP client configuration
   * @returns Promise<ApiResponseJsonNode> - Validation result
   */
  abstract validatePatient(data: JsonNode, configs?: HttpClientRequestConfig): Promise<ApiResponseJsonNode>;

  /**
   * Retrieves a Patient resource by ID
   * @param id - The Patient resource ID
   * @param configs - Optional HTTP client configuration
   * @returns Promise<JsonNode> - The Patient resource
   */
  abstract getPatientById(id: string, configs?: HttpClientRequestConfig): Promise<JsonNode>;

  /**
   * Soft deletes a Patient resource
   * @param id - The Patient resource ID to delete
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<ApiResponseJsonNode> - Deletion result
   */
  abstract deletePatient(
    id: string,
    body?: FhirAuditDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<ApiResponseJsonNode>;

  /**
   * Retrieves the history of a specific Patient resource
   * @param id - The Patient resource ID
   * @param historyParam - History query parameters
   * @param configs - Optional HTTP client configuration
   * @returns Promise<JsonNode> - The Patient resource history
   */
  abstract getPatientHistory(
    id: string,
    historyParam: GetHistoryRequestDto,
    configs?: HttpClientRequestConfig,
  ): Promise<JsonNode>;

  /**
   * Retrieves a specific version of a Patient resource
   * @param id - The Patient resource ID
   * @param vid - The version ID
   * @param configs - Optional HTTP client configuration
   * @returns Promise<JsonNode> - The specific version of Patient resource
   */
  abstract getPatientVersion(id: string, vid: number, configs?: HttpClientRequestConfig): Promise<JsonNode>;

  /**
   * Searches for Patient resources based on search parameters
   * @param params - Search parameters including pagination and filters
   * @param configs - Optional HTTP client configuration
   * @returns Promise<JsonNode> - Search results containing matching Patient resources
   */
  abstract searchPatients(params: PatientSearchParams, configs?: HttpClientRequestConfig): Promise<JsonNode>;

  /**
   * Advanced search for Patient resources using _search endpoint
   * @param params - Advanced search parameters with pagination and sorting
   * @param configs - Optional HTTP client configuration
   * @returns Promise<JsonNode> - Search results containing matching Patient resources
   */
  abstract searchPatientsAdvanced(
    params: PatientSearchAdvancedParams,
    configs?: HttpClientRequestConfig,
  ): Promise<JsonNode>;

  /**
   * Retrieves the complete history of all Patient resources
   * @param historyParam - History query parameters
   * @param configs - Optional HTTP client configuration
   * @returns Promise<JsonNode> - Complete Patient resources history
   */
  abstract getAllPatientsHistory(
    historyParam: GetHistoryRequestDto,
    configs?: HttpClientRequestConfig,
  ): Promise<JsonNode>;

  /**
   * Tạo mới một RelatedPerson.
   * @param dto Dữ liệu RelatedPerson cần tạo
   * @returns RelatedPerson đã được tạo
   */
  abstract createRelatedPatient(
    body: FhirRelatedPersonDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<FhirRelatedPersonDto>;

  /**
   * Tạo mới một Group Patient.
   * @param dto Dữ liệu Group cần tạo
   * @param configs Tuỳ chọn cấu hình cho request/service
   * @returns Group đã được tạo
   */
  abstract createGroupPatient(
    body: FhirGroupPatientCreatedDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<JsonNode>;

  /**
   * Retrieves a RelatedPerson resource by ID
   * @param id - The RelatedPerson resource ID
   * @param configs - Optional HTTP client configuration
   * @returns Promise<JsonNode> - The RelatedPerson resource
   */
  abstract getRelatedPersonById(id: string, configs?: HttpClientRequestConfig): Promise<JsonNode>;

  /**
   * Updates an existing FHIR RelatedPerson resource
   * @param data - The RelatedPerson resource data to update
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<FhirRelatedPersonDto> - Update result
   */
  abstract updateRelatedPerson(
    data: FhirRelatedPersonDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<FhirRelatedPersonDto>;

  /**
   * Soft deletes a RelatedPerson resource
   * @param id - The RelatedPerson resource ID to delete
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<ApiResponseJsonNode> - Deletion result
   */
  abstract deleteRelatedPerson(
    id: string,
    body?: FhirAuditDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<ApiResponseJsonNode>;
}
