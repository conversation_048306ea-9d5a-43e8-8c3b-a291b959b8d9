import { ApiProperty } from '@nestjs/swagger';
import { FhirGenderEnum, FhirResourceTypeEnum } from '../constants';
import { Expose, Type } from 'class-transformer';
import { IsOptional } from 'class-validator';
import {
  FhirCodeableConceptDto,
  FhirContactPointDto,
  FhirExtensionDto,
  FhirHumanNameDto,
  FhirIdentifierDto,
  FhirMetaDto,
  FhirReferenceDto,
  FhirAuditDto,
  FhirAttachmentDto,
} from './fhir-patient.dto';

export class FhirRelatedPersonDto {
  @ApiProperty({
    description: 'Loại tài nguyên',
    enum: FhirResourceTypeEnum,
    default: FhirResourceTypeEnum.RELATED_PERSON,
  })
  @Expose()
  resourceType: FhirResourceTypeEnum = FhirResourceTypeEnum.RELATED_PERSON;

  @ApiProperty({ description: 'ID của RelatedPerson', required: false })
  @IsOptional()
  @Expose()
  id?: string;

  @ApiProperty({ description: 'Thông tin meta', required: false, type: FhirMetaDto })
  @IsOptional()
  @Type(() => FhirMetaDto)
  @Expose()
  meta?: FhirMetaDto;

  @ApiProperty({ description: 'Danh sách extension', required: false, type: FhirExtensionDto, isArray: true })
  @IsOptional()
  @Type(() => FhirExtensionDto)
  @Expose()
  extension?: FhirExtensionDto[];

  @ApiProperty({ description: 'Định danh', required: false, type: FhirIdentifierDto, isArray: true })
  @IsOptional()
  @Type(() => FhirIdentifierDto)
  @Expose()
  identifier?: FhirIdentifierDto[];

  @ApiProperty({ description: 'Tham chiếu đến bệnh nhân', required: true, type: FhirReferenceDto })
  @Type(() => FhirReferenceDto)
  @Expose()
  patient: FhirReferenceDto;

  @ApiProperty({ description: 'Trạng thái hoạt động', required: false })
  @IsOptional()
  @Expose()
  active?: boolean;

  @ApiProperty({ description: 'Tên', required: false, type: FhirHumanNameDto, isArray: true })
  @IsOptional()
  @Type(() => FhirHumanNameDto)
  @Expose()
  name?: FhirHumanNameDto[];

  @ApiProperty({ description: 'Thông tin liên lạc', required: false, type: FhirContactPointDto, isArray: true })
  @IsOptional()
  @Type(() => FhirContactPointDto)
  @Expose()
  telecom?: FhirContactPointDto[];

  @ApiProperty({
    description: 'Mối quan hệ với bệnh nhân',
    required: false,
    type: FhirCodeableConceptDto,
    isArray: true,
  })
  @IsOptional()
  @Type(() => FhirCodeableConceptDto)
  @Expose()
  relationship?: FhirCodeableConceptDto[];

  @ApiProperty({ description: 'Thông tin audit', required: false, type: FhirAuditDto })
  @IsOptional()
  @Type(() => FhirAuditDto)
  @Expose()
  audit?: FhirAuditDto;

  @ApiProperty()
  @IsOptional()
  @Expose()
  gender?: string;

  @ApiProperty({ isArray: true, type: FhirAttachmentDto })
  @IsOptional()
  @Expose()
  @Type(() => FhirAttachmentDto)
  photo?: FhirAttachmentDto[];
}
