import { Expose, Transform } from 'class-transformer';
import { IsOptional, IsString, IsN<PERSON>ber, Min } from 'class-validator';

/**
 * Request DTO for getting FHIR resource history
 */
export class GetHistoryRequestDto {
  /**
   * Number of entries to return (pagination)
   */
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  @Expose()
  _count?: number;

  /**
   * Starting point for pagination
   */
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseInt(value))
  @Expose()
  _offset?: number;

  /**
   * Filter by last updated date (since)
   */
  @IsOptional()
  @IsString()
  @Expose()
  _since?: string;

  /**
   * Filter by last updated date (at)
   */
  @IsOptional()
  @IsString()
  @Expose()
  _at?: string;

  /**
   * List of tags to filter by
   */
  @IsOptional()
  @IsString()
  @Expose()
  _list?: string;
}
