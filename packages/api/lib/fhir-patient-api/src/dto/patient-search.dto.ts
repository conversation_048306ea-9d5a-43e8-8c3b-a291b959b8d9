import { Expose, Transform } from 'class-transformer';
import { IsOptional, IsString, IsNumber, Min, IsDateString, IsEnum } from 'class-validator';

/**
 * Gender enum for FHIR Patient search
 */
export enum PatientGender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
  UNKNOWN = 'unknown',
}

/**
 * Patient search parameters DTO
 * Based on FHIR R5 Patient search parameters
 */
export class PatientSearchParams {
  /**
   * Number of results to return
   */
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  @Expose()
  _count?: number;

  /**
   * Starting point for pagination
   */
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseInt(value))
  @Expose()
  _offset?: number;

  /**
   * Search by patient identifier
   */
  @IsOptional()
  @IsString()
  @Expose()
  identifier?: string;

  /**
   * Search by patient name (given or family)
   */
  @IsOptional()
  @IsString()
  @Expose()
  name?: string;

  /**
   * Search by family name
   */
  @IsOptional()
  @IsString()
  @Expose()
  family?: string;

  /**
   * Search by given name
   */
  @IsOptional()
  @IsString()
  @Expose()
  given?: string;

  /**
   * Search by gender
   */
  @IsOptional()
  @IsEnum(PatientGender)
  @Expose()
  gender?: PatientGender;

  /**
   * Search by birth date
   */
  @IsOptional()
  @IsDateString()
  @Expose()
  birthdate?: string;

  /**
   * Search by phone number
   */
  @IsOptional()
  @IsString()
  @Expose()
  phone?: string;

  /**
   * Search by email
   */
  @IsOptional()
  @IsString()
  @Expose()
  email?: string;

  /**
   * Search by address
   */
  @IsOptional()
  @IsString()
  @Expose()
  address?: string;

  /**
   * Search by city
   */
  @IsOptional()
  @IsString()
  @Expose()
  'address-city'?: string;

  /**
   * Search by state
   */
  @IsOptional()
  @IsString()
  @Expose()
  'address-state'?: string;

  /**
   * Search by postal code
   */
  @IsOptional()
  @IsString()
  @Expose()
  'address-postalcode'?: string;

  /**
   * Search by country
   */
  @IsOptional()
  @IsString()
  @Expose()
  'address-country'?: string;

  /**
   * Search by organization
   */
  @IsOptional()
  @IsString()
  @Expose()
  organization?: string;

  /**
   * Search active patients only
   */
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @Expose()
  active?: boolean;

  /**
   * Search by language
   */
  @IsOptional()
  @IsString()
  @Expose()
  language?: string;
}
