import { Expose, Transform } from 'class-transformer';
import { IsOptional, <PERSON>N<PERSON>ber, Min, IsString, IsObject } from 'class-validator';

/**
 * Advanced Patient search parameters DTO for _search endpoint
 * Based on OpenAPI spec for /fhir/r5/Patient/_search
 */
export class PatientSearchAdvancedParams {
  /**
   * Page number for pagination
   */
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseInt(value))
  @Expose()
  page?: number = 0;

  /**
   * Number of results per page
   */
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  @Expose()
  pageSize?: number = 10;

  /**
   * Field to sort by
   */
  @IsOptional()
  @IsString()
  @Expose()
  sortBy?: string;

  /**
   * Sort order (asc/desc)
   */
  @IsOptional()
  @IsString()
  @Expose()
  sortOrder?: string = '';

  /**
   * Search parameters object
   */
  @IsOptional()
  @IsObject()
  @Expose()
  searchParams?: Record<string, any>;
}
