import { ApiProperty } from '@nestjs/swagger';
import { FhirResourceTypeEnum } from '../constants';
import { Expose, Type } from 'class-transformer';
import { IsOptional } from 'class-validator';
import {
  FhirCodeableConceptDto,
  FhirCodingDto,
  FhirExtensionDto,
  FhirGroupMemberDto,
  FhirMetaDto,
  FhirReferenceDto,
} from './fhir-patient.dto';

export class FhirGroupPatientCreatedDto {
  @ApiProperty({
    description: 'Loại tài nguyên',
    enum: FhirResourceTypeEnum,
    default: FhirResourceTypeEnum.GROUP,
  })
  @Expose()
  @IsOptional()
  resourceType?: FhirResourceTypeEnum = FhirResourceTypeEnum.GROUP;

  @ApiProperty({ description: 'ID của Group', required: false })
  @IsOptional()
  @Expose()
  id?: string;

  @ApiProperty({ description: 'Thông tin meta', required: false, type: FhirMetaDto })
  @IsOptional()
  @Type(() => FhirMetaDto)
  @Expose()
  meta?: FhirMetaDto;

  @ApiProperty({ description: 'Danh sách extension', required: false, type: FhirExtensionDto, isArray: true })
  @IsOptional()
  @Type(() => FhirExtensionDto)
  @Expose()
  extension?: FhirExtensionDto[];

  @ApiProperty({ description: 'Tên nhóm', required: false })
  @IsOptional()
  @Expose()
  name?: string;

  @ApiProperty({ description: 'Trạng thái hoạt động', required: false })
  @IsOptional()
  @Expose()
  active?: boolean;

  @ApiProperty({ description: 'Loại nhóm (ví dụ: person)', required: false, example: 'person' })
  @IsOptional()
  @Expose()
  type?: string;

  @ApiProperty({ description: 'Kiểu thành viên (enumerated)', required: false, example: 'enumerated' })
  @IsOptional()
  @Expose()
  membership?: string;

  @ApiProperty({ type: FhirCodeableConceptDto })
  @IsOptional()
  @Expose()
  code?: FhirCodeableConceptDto;

  @ApiProperty({ description: 'Đơn vị quản lý', required: false, type: FhirReferenceDto })
  @IsOptional()
  @Type(() => FhirReferenceDto)
  @Expose()
  managingEntity?: FhirReferenceDto;

  @ApiProperty({ description: 'Danh sách thành viên', required: false, type: FhirGroupMemberDto, isArray: true })
  @IsOptional()
  @Type(() => FhirGroupMemberDto)
  @Expose()
  member?: FhirGroupMemberDto[];
}
