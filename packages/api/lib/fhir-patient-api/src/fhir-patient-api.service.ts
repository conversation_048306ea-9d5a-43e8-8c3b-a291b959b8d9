import { Injectable } from '@nestjs/common';
import { _checkExceptionFromAxiosRes, _interpolateString, _transformPlainToInstance } from '../../functions';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import {
  ApiResponseJsonNode,
  GetHistoryRequestDto,
  JsonNode,
  PatientSearchParams,
  PatientSearchAdvancedParams,
  FhirRelatedPersonDto,
  FhirGroupPatientCreatedDto,
  FhirPatientCreatedDto,
  FhirAuditDto,
} from './dto';
import { FhirPatientApiAbstract } from './fhir-patient-api.abstract';
import { FhirPatientApiEndpoint } from './fhir-patient-api.endpoint';

/**
 * FHIR Patient API Service
 * Implements FHIR R5 Patient resource operations
 */
@Injectable()
export class FhirPatientApiService extends FhirPatientApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  /**
   * Creates a new FHIR Patient resource
   * @param data - The Patient resource data to create
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<ApiResponseJsonNode> - Creation result
   */
  async createPatient(
    data: FhirPatientCreatedDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<FhirPatientCreatedDto> {
    const headers: any = {
      'Content-Type': 'application/fhir+json',
      'X-Requested-By': data?.audit?.createdBy,
      ...configs?.headers,
    };

    if (prefer) {
      headers['Prefer'] = prefer;
    }

    if (data?.audit) delete data?.audit; // xóa vì body  HC k cần

    const axiosRes = await this.httpClientService.post(FhirPatientApiEndpoint.PATIENT_CREATE, data, {
      ...configs,
      headers,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * Updates an existing FHIR Patient resource
   * @param data - The Patient resource data to update
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<ApiResponseJsonNode> - Update result
   */
  async updatePatient(
    data: FhirPatientCreatedDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<FhirPatientCreatedDto> {
    const headers: any = {
      'Content-Type': 'application/fhir+json',
      'X-Requested-By': data?.audit?.modifiedBy,
      ...configs?.headers,
    };

    if (prefer) {
      headers['Prefer'] = prefer;
    }

    if (data?.audit) delete data?.audit; // xóa vì body  HC k cần

    const axiosRes = await this.httpClientService.put(FhirPatientApiEndpoint.PATIENT_UPDATE, data, {
      ...configs,
      headers,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * Validates a FHIR Patient resource
   * @param data - The Patient resource data to validate
   * @param configs - Optional HTTP client configuration
   * @returns Promise<ApiResponseJsonNode> - Validation result
   */
  async validatePatient(data: JsonNode, configs?: HttpClientRequestConfig): Promise<ApiResponseJsonNode> {
    const axiosRes = await this.httpClientService.post(FhirPatientApiEndpoint.VALIDATE_PATIENT, data, {
      ...configs,
      headers: {
        'Content-Type': 'application/fhir+json',
        ...configs?.headers,
      },
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(ApiResponseJsonNode, axiosRes?.data || null) as ApiResponseJsonNode;
  }

  /**
   * Retrieves a Patient resource by ID
   * @param id - The Patient resource ID
   * @param configs - Optional HTTP client configuration
   * @returns Promise<JsonNode> - The Patient resource
   */
  async getPatientById(id: string, configs?: HttpClientRequestConfig): Promise<JsonNode> {
    const axiosRes = await this.httpClientService.get(_interpolateString(FhirPatientApiEndpoint.PATIENT_BY_ID, id), {
      ...configs,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  /**
   * Soft deletes a Patient resource
   * @param id - The Patient resource ID to delete
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<ApiResponseJsonNode> - Deletion result
   */
  async deletePatient(
    id: string,
    body?: FhirAuditDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<ApiResponseJsonNode> {
    const headers: any = {
      'Content-Type': 'application/fhir+json',
      'X-Requested-By': body?.modifiedBy,
      ...configs?.headers,
    };

    const axiosRes = await this.httpClientService.delete(
      _interpolateString(FhirPatientApiEndpoint.PATIENT_DELETE, id),
      { ...configs, headers },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(ApiResponseJsonNode, axiosRes?.data || null) as ApiResponseJsonNode;
  }

  /**
   * Retrieves the history of a specific Patient resource
   * @param id - The Patient resource ID
   * @param historyParam - History query parameters
   * @param configs - Optional HTTP client configuration
   * @returns Promise<JsonNode> - The Patient resource history
   */
  async getPatientHistory(
    id: string,
    historyParam: GetHistoryRequestDto,
    configs?: HttpClientRequestConfig,
  ): Promise<JsonNode> {
    const axiosRes = await this.httpClientService.get(_interpolateString(FhirPatientApiEndpoint.PATIENT_HISTORY, id), {
      ...configs,
      params: historyParam,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  /**
   * Retrieves a specific version of a Patient resource
   * @param id - The Patient resource ID
   * @param vid - The version ID
   * @param configs - Optional HTTP client configuration
   * @returns Promise<JsonNode> - The specific version of Patient resource
   */
  async getPatientVersion(id: string, vid: number, configs?: HttpClientRequestConfig): Promise<JsonNode> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(FhirPatientApiEndpoint.PATIENT_VERSION, id, vid.toString()),
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  /**
   * Searches for Patient resources based on search parameters
   * @param params - Search parameters including pagination and filters
   * @param configs - Optional HTTP client configuration
   * @returns Promise<JsonNode> - Search results containing matching Patient resources
   */
  async searchPatients(params: PatientSearchParams, configs?: HttpClientRequestConfig): Promise<JsonNode> {
    const axiosRes = await this.httpClientService.get(FhirPatientApiEndpoint.PATIENT_SEARCH, {
      ...configs,
      params: params,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  /**
   * Advanced search for Patient resources using _search endpoint
   * @param params - Advanced search parameters with pagination and sorting
   * @param configs - Optional HTTP client configuration
   * @returns Promise<JsonNode> - Search results containing matching Patient resources
   */
  async searchPatientsAdvanced(
    params: PatientSearchAdvancedParams,
    configs?: HttpClientRequestConfig,
  ): Promise<JsonNode> {
    const axiosRes = await this.httpClientService.get(FhirPatientApiEndpoint.PATIENT_SEARCH_POST, {
      ...configs,
      params: params,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  /**
   * Retrieves the complete history of all Patient resources
   * @param historyParam - History query parameters
   * @param configs - Optional HTTP client configuration
   * @returns Promise<JsonNode> - Complete Patient resources history
   */
  async getAllPatientsHistory(
    historyParam: GetHistoryRequestDto,
    configs?: HttpClientRequestConfig,
  ): Promise<JsonNode> {
    const axiosRes = await this.httpClientService.get(FhirPatientApiEndpoint.ALL_PATIENTS_HISTORY, {
      ...configs,
      params: historyParam,
      headers: {
        Accept: 'application/fhir+json',
        ...configs?.headers,
      },
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  /**
   * Creates a new FHIR Patient resource
   * @param data - The Patient resource data to create
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<ApiResponseJsonNode> - Creation result
   */
  async createRelatedPatient(
    data: FhirRelatedPersonDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<FhirRelatedPersonDto> {
    const headers: any = {
      'Content-Type': 'application/fhir+json',
      'X-Requested-By': data?.audit?.createdBy,
      ...configs?.headers,
    };
    if (prefer) {
      headers['Prefer'] = prefer;
    }
    if (data?.audit) delete data?.audit; // xóa vì body  HC k cần
    const axiosRes = await this.httpClientService.post(FhirPatientApiEndpoint.CREATE_RELATED_PERSON, data, {
      ...configs,
      headers,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(FhirRelatedPersonDto, axiosRes?.data || null) as FhirRelatedPersonDto;
  }

  /**
   * Creates a new FHIR Patient resource
   * @param data - The Patient resource data to create
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<ApiResponseJsonNode> - Creation result
   */
  async createGroupPatient(
    data: FhirGroupPatientCreatedDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<ApiResponseJsonNode> {
    const headers: any = {
      'Content-Type': 'application/fhir+json',
      ...configs?.headers,
    };
    if (prefer) {
      headers['Prefer'] = prefer;
    }
    const axiosRes = await this.httpClientService.post(FhirPatientApiEndpoint.CREATE_GROUP_PATIENT, data, {
      ...configs,
      headers,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(ApiResponseJsonNode, axiosRes?.data || null) as ApiResponseJsonNode;
  }

  /**
   * Retrieves a RelatedPerson resource by ID
   * @param id - The RelatedPerson resource ID
   * @param configs - Optional HTTP client configuration
   * @returns Promise<JsonNode> - The RelatedPerson resource
   */
  async getRelatedPersonById(id: string, configs?: HttpClientRequestConfig): Promise<JsonNode> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(FhirPatientApiEndpoint.GET_RELATED_PERSON_BY_ID, id),
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  /**
   * Updates an existing FHIR RelatedPerson resource
   * @param data - The RelatedPerson resource data to update
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<FhirRelatedPersonDto> - Update result
   */
  async updateRelatedPerson(
    data: FhirRelatedPersonDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<FhirRelatedPersonDto> {
    const headers: any = {
      'Content-Type': 'application/fhir+json',
      'X-Requested-By': data?.audit?.modifiedBy,
      ...configs?.headers,
    };

    if (prefer) {
      headers['Prefer'] = prefer;
    }
    if (data?.audit) delete data?.audit; // xóa vì body  HC k cần

    const axiosRes = await this.httpClientService.put(FhirPatientApiEndpoint.UPDATE_RELATED_PERSON, data, {
      ...configs,
      headers,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(FhirRelatedPersonDto, axiosRes?.data || null) as FhirRelatedPersonDto;
  }

  /**
   * Soft deletes a RelatedPerson resource
   * @param id - The RelatedPerson resource ID to delete
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<ApiResponseJsonNode> - Deletion result
   */
  async deleteRelatedPerson(
    id: string,
    body?: FhirAuditDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<ApiResponseJsonNode> {
    const headers: any = {
      'Content-Type': 'application/fhir+json',
      'X-Requested-By': body?.modifiedBy,
      ...configs?.headers,
    };
    const axiosRes = await this.httpClientService.delete(
      _interpolateString(FhirPatientApiEndpoint.DELETE_RELATED_PERSON, id),
      { ...configs, headers },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(ApiResponseJsonNode, axiosRes?.data || null) as ApiResponseJsonNode;
  }
}
