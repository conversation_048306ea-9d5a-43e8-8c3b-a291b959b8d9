import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MAX_REDIRECT, TIMEOUT } from '../../constants';
import { HttpClientModule } from '../../http-client/src';
import { FhirPatientApiService } from './fhir-patient-api.service';

/**
 * FHIR Patient API Module
 * Provides FHIR R5 Patient resource operations
 */
@Module({
  imports: [
    HttpClientModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        baseURL: configService.get('FHIR_SERVER_URL'),
        timeout: TIMEOUT,
        maxRedirects: MAX_REDIRECT,
        validateStatus: () => {
          return true;
        },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [FhirPatientApiService],
  exports: [FhirPatientApiService],
})
export class FhirPatientApiModule {}
