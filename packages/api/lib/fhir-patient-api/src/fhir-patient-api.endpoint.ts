/**
 * Fast Healthcare Interoperability Resources Patient API Endpoints
 * Defines all endpoint constants for Fast Healthcare Interoperability Resources R5 Patient resource operations
 */
export enum FhirPatientApiEndpoint {
  // Patient validation endpoint
  VALIDATE_PATIENT = 'Patient/$validate',

  // Patient CRUD operations
  PATIENT_CREATE = 'Patient',
  PATIENT_UPDATE = 'Patient',
  PATIENT_BY_ID = 'Patient/{0}',
  PATIENT_DELETE = 'Patient/{0}',
  PATIENT_SEARCH = 'Patient',
  PATIENT_SEARCH_POST = 'Patient/_search',

  CREATE_RELATED_PERSON = 'RelatedPerson',
  UPDATE_RELATED_PERSON = 'RelatedPerson',
  GET_RELATED_PERSON_BY_ID = 'RelatedPerson/{0}',
  DELETE_RELATED_PERSON = 'RelatedPerson/{0}',
  CREATE_GROUP_PATIENT = 'Group',

  // Patient history operations
  PATIENT_HISTORY = 'Patient/{0}/_history',
  PATIENT_VERSION = 'Patient/{0}/_history/{1}',
  ALL_PATIENTS_HISTORY = 'Patient/_history',
}
