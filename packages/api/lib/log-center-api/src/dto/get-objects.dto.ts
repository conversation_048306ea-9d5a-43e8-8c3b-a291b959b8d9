import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class GetObjectsDto {
  @Expose()
  @ApiProperty()
  fields: string;

  @Expose()
  @ApiProperty()
  per_page: number;

  @Expose()
  @ApiProperty()
  type: string;
}

export class KibanaObjectResponseDto {
  @Expose()
  @ApiProperty()
  page: number;

  @Expose()
  @ApiProperty()
  total: number;

  @Expose()
  @ApiProperty()
  per_page: number;

  @Expose()
  @ApiProperty()
  saved_objects: KibanaObjectDto[];
}

export class KibanaObjectDto {
  @Expose()
  @ApiProperty()
  type: string;

  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  attributes: Record<string, any>;

  @Expose()
  @ApiProperty({ type: [Object] })
  references: any[];

  @Expose()
  @ApiProperty()
  migrationVersion: Record<string, any>;

  @Expose()
  @ApiProperty()
  coreMigrationVersion: string;

  @Expose()
  @ApiProperty()
  updated_at: string;

  @Expose()
  @ApiProperty()
  version: string;

  @Expose()
  @ApiProperty({ type: [String] })
  namespaces: string[];

  @Expose()
  @ApiProperty()
  score: number;
}
