import { Injectable } from '@nestjs/common';
import { _checkExceptionFromAxiosRes, _transformPlainToInstance } from '../../functions';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import { GetObjectsDto, KibanaObjectResponseDto } from './dto';
import { LogCenterApiAbstract } from './log-center-api.abstract';
import { LogCenterApiEndpoint } from './log-center-api.endpoint';

/**
 * VAC Approval API Service
 * Implements approval ticket management operations
 */
@Injectable()
export class LogCenterApiService extends LogCenterApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  async getObjects(payload: GetObjectsDto, configs?: HttpClientRequestConfig): Promise<KibanaObjectResponseDto> {
    const axiosRes = await this.httpClientService.get(LogCenterApiEndpoint.GET_OBJECTS, {
      ...configs,
      params: payload,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data as KibanaObjectResponseDto;
  }
}
