export enum VacApprovalApiEndpoint {
  CREATE_APPROVAL_TICKET = 'api/approval-ticket',
  GET_ALL_APPROVAL_TICKETS = 'api/approval-ticket/get-all',
  CREATE_AND_APPROVE_TICKET = 'api/approval-ticket/create-and-approve',
  GET_APPROVE_TICKET_BY_CODE = 'api/approval-ticket/get-by-code',
  GET_APPROVE_TICKET_BY_TRANSACTION_CODE = 'api/approval-ticket/get-by-transaction-code',
  UPDATE_STATUS_APPROVAL_TICKET = 'api/approval-ticket/uppdate-status',
  UPDATE_STATUS_APPROVAL_TICKET_BY_TRANSACTION_CODE = 'api/approval-ticket/update-status-by-transaction-code',
}
