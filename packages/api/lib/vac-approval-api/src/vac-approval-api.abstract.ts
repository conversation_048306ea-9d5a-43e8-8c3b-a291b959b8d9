import { HttpClientRequestConfig } from '../../http-client/src';
import {
  ApprovalTicketItemDto,
  CreateApprovalTicketDto,
  CreateApprovalTicketResponseDto,
  GetAllApprovalTicketsDto,
  GetAllApprovalTicketsResponseDto,
  UpdateStatusApprovalTicketDto,
  UpdateApprovalTicketResponseDto,
  UpdateStatusApprovalTicketByTransactionCodeDto,
} from './dto';

export abstract class VacApprovalApiAbstract {
  /**
   * Creates a new approval ticket
   * @param data - The approval ticket data to create
   * @param configs - Optional HTTP client configuration
   * @returns Promise<CreateApprovalTicketResponseDto> - Creation result
   */
  abstract createApprovalTicket(
    data: CreateApprovalTicketDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CreateApprovalTicketResponseDto>;

  /**
   * Gets all approval tickets with filtering options
   * @param data - The search parameters for filtering tickets
   * @param configs - Optional HTTP client configuration
   * @returns Promise<GetAllApprovalTicketsResponseDto> - List of approval tickets
   */
  abstract getAllApprovalTickets(
    data: GetAllApprovalTicketsDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetAllApprovalTicketsResponseDto>;

  /**
   * Creates a new approval ticket and approves it immediately
   * @param data - The approval ticket data to create
   * @param configs - Optional HTTP client configuration
   * @returns Promise<CreateApprovalTicketResponseDto> - Creation result
   */
  abstract createAndApproveTicket(
    data: CreateApprovalTicketDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CreateApprovalTicketResponseDto>;

  /**
   * Get ticket approval details by code
   * @param code - The search parameters for filtering tickets
   * @param configs - Optional HTTP client configuration
   * @returns Promise<GetAllApprovalTicketsResponseDto> - List of approval tickets
   */
  abstract getDetailApprovalTickets(code: string, configs?: HttpClientRequestConfig): Promise<ApprovalTicketItemDto>;

  abstract getDetailApprovalTicketsByTransactionCode(transactionCode: string, configs?: HttpClientRequestConfig): Promise<ApprovalTicketItemDto>;

  /**
   * Updates an existing approval ticket
   * @param data - The approval ticket data to update
   * @param configs - Optional HTTP client configuration
   * @returns Promise<UpdateApprovalTicketResponseDto> - Update result
   */
  abstract updateStatusApprovalTicket(
    data: UpdateStatusApprovalTicketDto,
    configs?: HttpClientRequestConfig,
  ): Promise<UpdateApprovalTicketResponseDto>;

  abstract updateStatusApprovalTicketByTransactionCode(
    data: UpdateStatusApprovalTicketByTransactionCodeDto,
    configs?: HttpClientRequestConfig,
  ): Promise<ApprovalTicketItemDto>;
}
