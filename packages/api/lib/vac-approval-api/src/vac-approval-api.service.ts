import { Injectable } from '@nestjs/common';
import { _checkExceptionFromAxiosRes, _transformPlainToInstance } from '../../functions';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import {
  ApprovalTicketItemDto,
  CreateApprovalTicketDto,
  CreateApprovalTicketResponseDto,
  GetAllApprovalTicketsDto,
  GetAllApprovalTicketsResponseDto,
  UpdateStatusApprovalTicketDto,
  UpdateApprovalTicketResponseDto,
  UpdateStatusApprovalTicketByTransactionCodeDto,
} from './dto';
import { VacApprovalApiAbstract } from './vac-approval-api.abstract';
import { VacApprovalApiEndpoint } from './vac-approval-api.endpoint';

/**
 * VAC Approval API Service
 * Implements approval ticket management operations
 */
@Injectable()
export class VacApprovalApiService extends VacApprovalApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  /**
   * Creates a new approval ticket
   * @param data - The approval ticket data to create
   * @param configs - Optional HTTP client configuration
   * @returns Promise<CreateApprovalTicketResponseDto> - Creation result
   */
  async createApprovalTicket(
    data: CreateApprovalTicketDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CreateApprovalTicketResponseDto> {
    const axiosRes = await this.httpClientService.post(VacApprovalApiEndpoint.CREATE_APPROVAL_TICKET, data, configs);
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(
      CreateApprovalTicketResponseDto,
      axiosRes?.data,
    ) as CreateApprovalTicketResponseDto;
  }

  /**
   * Gets all approval tickets with filtering options
   * @param data - The search parameters for filtering tickets
   * @param configs - Optional HTTP client configuration
   * @returns Promise<GetAllApprovalTicketsResponseDto> - List of approval tickets
   */
  async getAllApprovalTickets(
    data: GetAllApprovalTicketsDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetAllApprovalTicketsResponseDto> {
    const axiosRes = await this.httpClientService.post(VacApprovalApiEndpoint.GET_ALL_APPROVAL_TICKETS, data, configs);
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data as GetAllApprovalTicketsResponseDto;
  }

  /**
   * Creates a new approval ticket
   * @param data - The approval ticket data to create
   * @param configs - Optional HTTP client configuration
   * @returns Promise<CreateApprovalTicketResponseDto> - Creation result
   */
  async createAndApproveTicket(
    data: CreateApprovalTicketDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CreateApprovalTicketResponseDto> {
    const axiosRes = await this.httpClientService.post(VacApprovalApiEndpoint.CREATE_AND_APPROVE_TICKET, data, configs);
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(
      CreateApprovalTicketResponseDto,
      axiosRes?.data,
    ) as CreateApprovalTicketResponseDto;
  }

  /**
   * Get ticket approval details by code
   * @param code - The search parameters for filtering tickets
   * @param configs - Optional HTTP client configuration
   * @returns Promise<GetAllApprovalTicketsResponseDto> - List of approval tickets
   */
  async getDetailApprovalTickets(code: string, configs?: HttpClientRequestConfig): Promise<ApprovalTicketItemDto> {
    const axiosRes = await this.httpClientService.get(
      `${VacApprovalApiEndpoint.GET_APPROVE_TICKET_BY_CODE}?code=${code}`,
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * Get ticket approval details by transaction code
   * @param transactionCode - The search parameters for filtering tickets
   * @param configs - Optional HTTP client configuration
   * @returns Promise<GetAllApprovalTicketsResponseDto> - List of approval tickets
   */
  async getDetailApprovalTicketsByTransactionCode(
    transactionCode: string,
    configs?: HttpClientRequestConfig,
  ): Promise<ApprovalTicketItemDto> {
    const axiosRes = await this.httpClientService.get(
      `${VacApprovalApiEndpoint.GET_APPROVE_TICKET_BY_TRANSACTION_CODE}?transactionCode=${transactionCode}`,
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * Updates an existing approval ticket
   * @param data - The approval ticket data to update
   * @param configs - Optional HTTP client configuration
   * @returns Promise<UpdateApprovalTicketResponseDto> - Update result
   */
  async updateStatusApprovalTicket(
    data: UpdateStatusApprovalTicketDto,
    configs?: HttpClientRequestConfig,
  ): Promise<UpdateApprovalTicketResponseDto> {
    const axiosRes = await this.httpClientService.put(
      VacApprovalApiEndpoint.UPDATE_STATUS_APPROVAL_TICKET,
      data,
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return { success: axiosRes?.data } as UpdateApprovalTicketResponseDto;
  }

  /**
   * Updates an existing approval ticket
   * @param data - The approval ticket data to update
   * @param configs - Optional HTTP client configuration
   * @returns Promise<UpdateApprovalTicketResponseDto> - Update result
   */
  async updateStatusApprovalTicketByTransactionCode(
    data: UpdateStatusApprovalTicketByTransactionCodeDto,
    configs?: HttpClientRequestConfig,
  ): Promise<ApprovalTicketItemDto> {
    const axiosRes = await this.httpClientService.put(
      VacApprovalApiEndpoint.UPDATE_STATUS_APPROVAL_TICKET_BY_TRANSACTION_CODE,
      data,
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }
}
