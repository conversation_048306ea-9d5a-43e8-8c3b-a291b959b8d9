import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { <PERSON><PERSON>rra<PERSON>, IsN<PERSON>ber, IsOptional, IsString, ValidateNested } from 'class-validator';

export class ApprovalTicketDetailDto {
  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  approvalTicketId?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  scheduleId?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  sku?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  skuName?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  orderDetailAttachmentCode?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  injection?: number;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  regimenId?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  regimenName?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  diseaseGroupId?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  diseaseGroupName?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  appointmentDate?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  type?: number;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  createdBy?: string;
}

export class CreateApprovalTicketDto {
  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  injectedPersonLCV?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  shopCode?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  shopName?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  name?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  personPhone?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  personDOB?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  type?: number;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  note?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  createdBy?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  createdByName?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  modifiedBy?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  modifiedByName?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  approvalTicketType?: number;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  approvalType?: number;

  @Expose()
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ApprovalTicketDetailDto)
  @ApiProperty({ type: [ApprovalTicketDetailDto] })
  approvalTicketDetails: ApprovalTicketDetailDto[];
}

export class CreateApprovalTicketResponseDto {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  createdBy: string;

  @Expose()
  @ApiProperty()
  createdDate: string;

  @Expose()
  @ApiProperty()
  modifiedBy: string;

  @Expose()
  @ApiProperty()
  modifiedDate: string;

  @Expose()
  @ApiProperty()
  code: string;

  @Expose()
  @ApiProperty()
  injectedPersonLCV: string;

  @Expose()
  @ApiProperty()
  personPhone: string;

  @Expose()
  @ApiProperty()
  personDOB: string;

  @Expose()
  @ApiProperty()
  shopCode: string;

  @Expose()
  @ApiProperty()
  shopName: string;

  @Expose()
  @ApiProperty()
  name: string;

  @Expose()
  @ApiProperty()
  status: number;

  @Expose()
  @ApiProperty()
  type: number;

  @Expose()
  @ApiProperty()
  note: string;

  @Expose()
  @ApiProperty()
  createdByName: string;

  @Expose()
  @ApiProperty()
  modifiedByName: string;
}
