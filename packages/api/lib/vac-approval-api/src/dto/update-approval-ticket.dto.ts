import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';

export class ApprovalTicketImageUpdateDto {
  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  note: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  imageUrl: string;
}

export class ApprovalTicketCommentUpdateDto {
  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  comment: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  note: string;
}

export class UpdateStatusApprovalTicketDto {
  @Expose()
  @ApiProperty()
  @IsString()
  id: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  note?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  status?: number;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  modifiedBy?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  modifiedByName?: string;
}

export class UpdateStatusApprovalTicketByTransactionCodeDto {
  @Expose()
  @ApiProperty()
  @IsString()
  transactionCode: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  note?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  status?: number;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  modifiedBy?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  modifiedByName?: string;

  @Expose()
  @ApiProperty({ type: [ApprovalTicketImageUpdateDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ApprovalTicketImageUpdateDto)
  approvalTicketImages?: ApprovalTicketImageUpdateDto[];

  @Expose()
  @ApiProperty({ type: [ApprovalTicketCommentUpdateDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ApprovalTicketCommentUpdateDto)
  approvalTicketComments?: ApprovalTicketCommentUpdateDto[];
}

export class UpdateApprovalTicketResponseDto {
  @Expose()
  @ApiProperty()
  success: boolean;
}
