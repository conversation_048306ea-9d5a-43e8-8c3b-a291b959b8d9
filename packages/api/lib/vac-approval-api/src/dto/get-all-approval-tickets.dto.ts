import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsNumber, IsOptional, IsString } from 'class-validator';

export class GetAllApprovalTicketsDto {
  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  maxResultCount?: number;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  skipCount?: number;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  keyword?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  shopCode?: string;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  employeeCode?: string[];

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  fromTime?: Date;

  @Expose()
  @ApiProperty()
  @IsOptional()
  @IsString()
  toTime?: Date;

  @Expose()
  @ApiProperty({ example: [1] })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  ticketStatus?: number[];

  @Expose()
  @ApiProperty({ example: [1] })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  ticketType?: number[];

  @Expose()
  @ApiProperty({ example: [1] })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  approvalType?: number[];
}

export class ApprovalTicketDetailResponseDto {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  approvalTicketId: string;

  @Expose()
  @ApiProperty()
  scheduleId: string;

  @Expose()
  @ApiProperty()
  sku: string;

  @Expose()
  @ApiProperty()
  skuName: string;

  @Expose()
  @ApiProperty()
  orderDetailAttachmentCode: string;

  @Expose()
  @ApiProperty()
  injection: number;

  @Expose()
  @ApiProperty()
  regimenId: string;

  @Expose()
  @ApiProperty()
  regimenName: string;

  @Expose()
  @ApiProperty()
  diseaseGroupId: string;

  @Expose()
  @ApiProperty()
  diseaseGroupName: string;

  @Expose()
  @ApiProperty()
  appointmentDate: Date;

  @Expose()
  @ApiProperty()
  type: number;

  @Expose()
  @ApiProperty()
  createdBy: string;

  @Expose()
  @ApiProperty()
  createdDate: Date;

  @Expose()
  @ApiProperty()
  modifiedBy: string;

  @Expose()
  @ApiProperty()
  modifiedDate: Date;
}

export class ApprovalTicketStepDto {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  approvalTicketId: string;

  @Expose()
  @ApiProperty()
  employeeCode: string;

  @Expose()
  @ApiProperty()
  employeeName: string;

  @Expose()
  @ApiProperty()
  step: number;

  @Expose()
  @ApiProperty()
  note: string;

  @Expose()
  @ApiProperty()
  createdDate: Date;

  @Expose()
  @ApiProperty()
  modifiedDate: Date;
}

export class ApprovalTicketImageDto {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  approvalTicketId: string;

  @Expose()
  @ApiProperty()
  imageUrl: string;

  @Expose()
  @ApiProperty()
  note: string;

  @Expose()
  @ApiProperty()
  isDeleted: boolean;

  @Expose()
  @ApiProperty()
  createdBy: string;

  @Expose()
  @ApiProperty()
  createdDate: Date;

  @Expose()
  @ApiProperty()
  modifiedBy: string;

  @Expose()
  @ApiProperty()
  modifiedDate: Date;
}

export class ApprovalTicketCommentDto {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  approvalTicketId: string;

  @Expose()
  @ApiProperty()
  comment: string;

  @Expose()
  @ApiProperty()
  note: string;

  @Expose()
  @ApiProperty()
  isDeleted: boolean;

  @Expose()
  @ApiProperty()
  createdBy: string;

  @Expose()
  @ApiProperty()
  createdDate: Date;

  @Expose()
  @ApiProperty()
  modifiedBy: string;

  @Expose()
  @ApiProperty()
  modifiedDate: Date;
}

export class ApprovalTicketItemDto {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  code: string;

  @Expose()
  @ApiProperty()
  injectedPersonLCV: string;

  @Expose()
  @ApiProperty()
  shopCode: string;

  @Expose()
  @ApiProperty()
  shopName: string;

  @Expose()
  @ApiProperty()
  name: string;

  @Expose()
  @ApiProperty()
  personPhone: string;

  @Expose()
  @ApiProperty()
  personDOB: Date;

  @Expose()
  @ApiProperty()
  status: number;

  @Expose()
  @ApiProperty()
  type: number;

  @Expose()
  @ApiProperty()
  note: string;

  @Expose()
  @ApiProperty()
  createdBy: string;

  @Expose()
  @ApiProperty()
  createdByName: string;

  @Expose()
  @ApiProperty()
  createdDate: Date;

  @Expose()
  @ApiProperty()
  modifiedBy: string;

  @Expose()
  @ApiProperty()
  modifiedDate: Date;

  @Expose()
  @ApiProperty()
  modifiedByName: string;

  @Expose()
  @ApiProperty()
  approvalTicketType: number;

  @Expose()
  @ApiProperty()
  approvalType: number;

  @Expose()
  @Type(() => ApprovalTicketDetailResponseDto)
  @ApiProperty({ type: [ApprovalTicketDetailResponseDto] })
  approvalTicketDetails: ApprovalTicketDetailResponseDto[];

  @Expose()
  @Type(() => ApprovalTicketStepDto)
  @ApiProperty({ type: [ApprovalTicketStepDto] })
  approvalTicketSteps: ApprovalTicketStepDto[];

  @Expose()
  @Type(() => ApprovalTicketImageDto)
  @ApiProperty({ type: [ApprovalTicketImageDto] })
  approvalTicketImages: ApprovalTicketImageDto[];

  @Expose()
  @Type(() => ApprovalTicketCommentDto)
  @ApiProperty({ type: [ApprovalTicketCommentDto] })
  approvalTicketComments: ApprovalTicketCommentDto[];
}

export class GetAllApprovalTicketsResponseDto {
  @Expose()
  @Type(() => ApprovalTicketItemDto)
  @ApiProperty({ type: [ApprovalTicketItemDto] })
  items: ApprovalTicketItemDto[];

  @Expose()
  @ApiProperty()
  totalCount: number;
}
