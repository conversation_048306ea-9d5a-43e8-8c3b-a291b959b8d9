import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MAX_REDIRECT, TIMEOUT } from '../../constants';
import { HttpClientModule } from '../../http-client/src';
import { VacApprovalApiService } from './vac-approval-api.service';

@Module({
  imports: [
    HttpClientModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        baseURL: configService.get('VAC_APPROVAL_API_URL'),
        timeout: TIMEOUT,
        maxRedirects: MAX_REDIRECT,
        validateStatus: () => {
          return true;
        },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [VacApprovalApiService],
  exports: [VacApprovalApiService],
})
export class VacApprovalApiModule {}
