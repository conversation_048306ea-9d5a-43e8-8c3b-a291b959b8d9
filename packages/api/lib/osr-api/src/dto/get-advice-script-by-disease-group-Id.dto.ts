import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { ArrayNotEmpty, IsArray, IsString } from 'class-validator';

export class AffiliateAdviceScriptDto {
  @ApiProperty({})
  @Transform(({ value }) => (Array.isArray(value) ? value : [value]))
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  diseaseGroupIds: string[];
}

export class AffiliateAdviceScriptRes {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  sku: string;

  @ApiProperty()
  @Expose()
  skuName: string;

  @ApiProperty()
  @Expose()
  audience: string;

  @ApiProperty()
  @Expose()
  adviceScript: string;

  @ApiProperty()
  @Expose()
  displayOrder: number;

  @ApiProperty()
  @Expose()
  status: number;

  @ApiProperty()
  @Expose()
  diseaseGroupId: string;

  @ApiProperty()
  @Expose()
  diseaseGroupName: string;
}
