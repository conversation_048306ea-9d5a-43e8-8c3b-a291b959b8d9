import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, IsArray, IsUrl } from 'class-validator';

export class CompanyEmailDto {
  @ApiProperty({ example: '396b7c42-adb1-451a-8f93-f97ca455573a' })
  @IsString()
  id: string;

  @ApiProperty({ example: '7258a294-a90c-401a-b7de-0d7f814f5894' })
  @IsString()
  companyId: string;

  @ApiProperty({ example: 'fpt.com' })
  @IsString()
  emailDomain: string;

  @ApiProperty({ example: null, required: false })
  @IsOptional()
  @IsString()
  subEmail?: string | null;

  @ApiProperty({ example: '2023-08-17T15:17:47.839' })
  @IsString()
  createdDate: string;

  @ApiProperty({ example: null, required: false })
  @IsOptional()
  @IsString()
  modifiedDate?: string | null;
}

export class CompanyDto {
  @ApiProperty({ example: '7258a294-a90c-401a-b7de-0d7f814f5894' })
  @IsString()
  id: string;

  @ApiProperty({ example: 'Công ty cổ phần FPT (FPT HO)' })
  @IsString()
  name: string;

  @ApiProperty({ example: 'https://s3-sgn09.fptcloud.com/public-bank-logo/aec/fpt.jpg' })
  @IsUrl()
  imageUrl: string;

  @ApiProperty({ example: false })
  @IsBoolean()
  flag: boolean;

  @ApiProperty({ example: '2023-08-17T15:07:24.55' })
  @IsString()
  createdDate: string;

  @ApiProperty({ example: '' })
  @IsString()
  createdBy: string;

  @ApiProperty({ example: null, required: false })
  @IsOptional()
  @IsString()
  modifiedDate?: string | null;

  @ApiProperty({ example: '' })
  @IsString()
  updatedBy: string;

  @ApiProperty({ example: true })
  @IsBoolean()
  isActive: boolean;

  @ApiProperty({ type: [CompanyEmailDto] })
  @IsArray()
  emails: CompanyEmailDto[];
}

export class CompanyListDto {
  @ApiProperty({ type: [CompanyDto] })
  @IsArray()
  items: CompanyDto[];
}

export class VerifyEmailOtpPayloadDto {
  @ApiProperty({ example: '7258a294-a90c-401a-b7de-0d7f814f5894' })
  @IsString()
  companyId: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsString()
  email: string;
}
export class CompanyNotifyMethodDto {
  @ApiProperty({ description: 'Phương thức thông báo của công ty', type: [String] })
  companyNotifyMethod: string[];
}

export class VerifyCompanyEmailDataDto {
  @ApiProperty({ description: 'Trạng thái hợp lệ của email' })
  isValid: boolean;

  @ApiProperty({ description: 'Danh sách phương thức thông báo của công ty', type: [String] })
  companyNotifyMethod: string[];
}

export class VerifyCompanyEmailResDto {
  @ApiProperty({ description: 'Trạng thái hợp lệ của email' })
  isValid: boolean;

  @ApiProperty({ description: 'Thông báo kết quả kiểm tra email' })
  message: string;

  @ApiProperty({ description: 'Thông tin bổ sung về công ty', type: VerifyCompanyEmailDataDto })
  data: VerifyCompanyEmailDataDto;
}
