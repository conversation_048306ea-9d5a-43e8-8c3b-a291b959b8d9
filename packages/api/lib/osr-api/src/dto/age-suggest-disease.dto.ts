import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsBoolean, IsNumber, IsString } from 'class-validator';

export class AgeSuggestDiseaseRes {
  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  id?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  comboName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  diseaseGroupId?: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  @IsOptional()
  ageFromValue?: number;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  ageFromName?: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  @IsOptional()
  ageToValue?: number;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  ageToName?: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  @IsOptional()
  priority?: number;

  @ApiProperty()
  @Expose()
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  createdTime?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  createdBy?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  modifiedDate?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  modifiedBy?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  sku?: string;
}
