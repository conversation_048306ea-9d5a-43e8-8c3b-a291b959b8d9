import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class VerifyQuotaItem {
  @ApiProperty({
    description: 'List of promotion codes',
    type: [String],
  })
  @Expose()
  promotionCodes: string[];

  @ApiProperty({
    description: 'Order code',
    type: String,
  })
  @Expose()
  orderCode: string;
}

export class VerifyQuotaByListDto {
  @ApiProperty({
    description: 'Email address',
    type: String,
  })
  @Expose()
  email: string;

  @ApiProperty({
    description: 'List of quota verifications',
    type: [VerifyQuotaItem],
  })
  @Type(() => VerifyQuotaItem)
  @Expose()
  verifies: VerifyQuotaItem[];
}

export class VerifyQuotaResult {
  @ApiProperty({
    description: 'Whether the verification is valid',
    type: Boolean,
  })
  @Expose()
  isValid: boolean;

  @ApiProperty({
    description: 'Message from verification',
    type: String,
  })
  @Expose()
  message: string;

  @ApiProperty({
    description: 'Order code',
    type: String,
  })
  @Expose()
  orderCode: string;
}

export class VerifyQuotaByListResDto {
  @ApiProperty({
    description: 'Email address',
    type: String,
  })
  @Expose()
  email: string;

  @ApiProperty({
    description: 'List of quota verification results',
    type: [VerifyQuotaResult],
  })
  @Type(() => VerifyQuotaResult)
  @Expose()
  verifyQuotaResDto: VerifyQuotaResult[];
}
