import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export interface quotaResponse {
  id: string;
  companyId: string;
  emailDomain: string;
  categoriesId: string;
  categoriesName: string;
  productTypeId: string;
  productTypeName: string;
  branchId: string;
  branchName: string;
  groupId: string;
  groupName: string;
  quantity: number;
  fromDate: string;
  toDate: string;
  isActive: boolean;
  isApplyAllEmail: boolean;
  promotionId: string;
  promotionName: string;
  quotaItems: QuotaItem[];
}

export class QuotaItem {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  quotaId: string;

  @ApiProperty()
  @Expose()
  sku: string;

  @ApiProperty()
  @Expose()
  skuName: string;

  @ApiProperty()
  @Expose()
  status: boolean;

  @ApiProperty()
  @Expose()
  createdDate: string;
}

export class GetRemainingQuotaPromotionReqDto {
  @ApiProperty()
  @Expose()
  companyId?: string;

  @ApiProperty()
  @Expose()
  email?: string;
}

export class GetRemainingQuotaPromotionResDto {
  @ApiProperty()
  @Expose()
  remainingQuota: number;

  @ApiProperty()
  @Expose()
  quotaResponse: quotaResponse;
}

export class VerifyQuotaPromotionReqDto {
  @ApiProperty()
  @Expose()
  email?: string;

  @ApiProperty()
  @Expose()
  promotionCodes?: string[];

  @ApiProperty()
  @Expose()
  orderCode?: string;
}

export class VerifyQuotaPromotionResDto {
  @ApiProperty()
  @Expose()
  isValid: boolean;

  @ApiProperty()
  @Expose()
  message: string;
}
