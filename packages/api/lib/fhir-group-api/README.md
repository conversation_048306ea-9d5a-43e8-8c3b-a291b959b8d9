# FHIR Patient API

This module provides a NestJS service for interacting with FHIR R5 Patient resources. It implements standard FHIR operations for Patient resource management.

## Features

- ✅ Patient resource validation
- ✅ Patient CRUD operations (Create, Read, Update, Delete)
- ✅ Patient search with comprehensive parameters
- ✅ Advanced patient search with pagination and sorting
- ✅ Patient history tracking
- ✅ Version-specific Patient retrieval
- ✅ Full FHIR R5 compliance
- ✅ TypeScript support with DTOs
- ✅ Comprehensive JSDoc documentation

## Installation

```bash
npm install
```

## Configuration

Add the following environment variable to your `.env` file:

```env
FHIR_SERVER_URL=https://your-fhir-server.com/fhir
```

## Usage

### Import the Module

```typescript
import { Module } from '@nestjs/common';
import { FhirPatientApiModule } from '@your-org/nestjs-frt-api';

@Module({
  imports: [FhirPatientApiModule],
})
export class AppModule {}
```

### Inject the Service

```typescript
import { Injectable } from '@nestjs/common';
import {
  FhirPatientApiService,
  PatientSearchParams,
  PatientSearchAdvancedParams,
  JsonNode,
} from '@your-org/nestjs-frt-api';

@Injectable()
export class PatientService {
  constructor(private readonly fhirPatientApiService: FhirPatientApiService) {}

  async createPatient(patientData: JsonNode) {
    return await this.fhirPatientApiService.createPatient(patientData, 'return=representation');
  }

  async updatePatient(patientData: JsonNode) {
    return await this.fhirPatientApiService.updatePatient(patientData, 'return=representation');
  }

  async searchPatients() {
    const searchParams: PatientSearchParams = {
      name: 'John',
      gender: 'male',
      _count: 10,
    };

    return await this.fhirPatientApiService.searchPatients(searchParams);
  }

  async searchPatientsAdvanced() {
    const searchParams: PatientSearchAdvancedParams = {
      page: 0,
      pageSize: 20,
      sortBy: 'name',
      sortOrder: 'asc',
      searchParams: {
        name: 'John',
        gender: 'male',
      },
    };

    return await this.fhirPatientApiService.searchPatientsAdvanced(searchParams);
  }

  async getPatient(id: string) {
    return await this.fhirPatientApiService.getPatientById(id);
  }
}
```

## API Methods

### Patient Operations

#### `createPatient(data: JsonNode, prefer?: string, configs?: HttpClientRequestConfig): Promise<ApiResponseJsonNode>`

Creates a new FHIR Patient resource. The `prefer` parameter can be used to control the response format (e.g., 'return=representation').

#### `updatePatient(data: JsonNode, prefer?: string, configs?: HttpClientRequestConfig): Promise<ApiResponseJsonNode>`

Updates an existing FHIR Patient resource. The `prefer` parameter can be used to control the response format (e.g., 'return=representation').

#### `validatePatient(data: JsonNode, configs?: HttpClientRequestConfig): Promise<ApiResponseJsonNode>`

Validates a FHIR Patient resource against the server's validation rules.

#### `getPatientById(id: string, configs?: HttpClientRequestConfig): Promise<JsonNode>`

Retrieves a specific Patient resource by its ID.

#### `deletePatient(id: string, prefer?: string, configs?: HttpClientRequestConfig): Promise<ApiResponseJsonNode>`

Soft deletes a Patient resource. The `prefer` parameter can be used to control the response format.

#### `searchPatients(params: PatientSearchParams, configs?: HttpClientRequestConfig): Promise<JsonNode>`

Searches for Patient resources based on various criteria including:

- Name (given, family)
- Gender
- Birth date
- Contact information (phone, email)
- Address details
- Identifiers
- Organization
- Active status

#### `searchPatientsAdvanced(params: PatientSearchAdvancedParams, configs?: HttpClientRequestConfig): Promise<JsonNode>`

Advanced search for Patient resources using the `_search` endpoint with enhanced pagination and sorting capabilities:

- `page`: Page number for pagination (default: 0)
- `pageSize`: Number of results per page (default: 10)
- `sortBy`: Field to sort by
- `sortOrder`: Sort order (asc/desc)
- `searchParams`: Object containing search criteria

#### `getPatientHistory(id: string, historyParam: GetHistoryRequestDto, configs?: HttpClientRequestConfig): Promise<JsonNode>`

Retrieves the complete history of changes for a specific Patient resource.

#### `getPatientVersion(id: string, vid: number, configs?: HttpClientRequestConfig): Promise<JsonNode>`

Retrieves a specific version of a Patient resource.

#### `getAllPatientsHistory(historyParam: GetHistoryRequestDto, configs?: HttpClientRequestConfig): Promise<JsonNode>`

Retrieves the complete history of all Patient resources.

## Search Parameters

The `PatientSearchParams` DTO supports the following search criteria:

- `identifier`: Patient identifier
- `name`: Patient name (searches both given and family names)
- `family`: Family name
- `given`: Given name
- `gender`: Patient gender (male, female, other, unknown)
- `birthdate`: Birth date
- `phone`: Phone number
- `email`: Email address
- `address`: Address
- `address-city`: City
- `address-state`: State
- `address-postalcode`: Postal code
- `address-country`: Country
- `organization`: Associated organization
- `active`: Active status
- `language`: Language
- `_count`: Number of results to return
- `_offset`: Starting point for pagination

## Error Handling

The service automatically handles HTTP errors and throws `CoreException` for non-successful responses. Make sure to wrap your calls in try-catch blocks:

```typescript
try {
  const patient = await this.fhirPatientApiService.getPatientById('123');
} catch (error) {
  if (error instanceof CoreException) {
    console.error('FHIR API Error:', error.message);
  }
}
```

## FHIR Compliance

This module is designed to work with FHIR R5 servers and follows FHIR specifications for:

- Resource structure
- Search parameters
- HTTP status codes
- Content types (`application/fhir+json`)
- Error responses

## Contributing

When contributing to this module, please ensure:

- All methods are properly documented with JSDoc
- DTOs are validated with class-validator decorators
- Error handling follows the established patterns
- Tests are written for new functionality
