import { Injectable } from '@nestjs/common';
import { _checkExceptionFromAxiosRes, _interpolateString, _transformPlainToInstance } from '../../functions';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import { FhirGroupPatientCreatedDto } from './dto';
import { FhirGroupPatientApiAbstract } from './fhir-group-api.abstract';
import { FhirGroupPatientApiEndpoint } from './fhir-group-api.endpoint';

/**
 * FHIR Patient API Service
 * Implements FHIR R5 Patient resource operations
 */
@Injectable()
export class FhirGroupPatientApiService extends FhirGroupPatientApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  /**
   * Creates a new FHIR Patient resource
   * @param data - The Patient resource data to create
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<ApiResponseJsonNode> - Creation result
   */
  async createGroupPatient(
    data: FhirGroupPatientCreatedDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<FhirGroupPatientCreatedDto> {
    const headers: any = {
      'Content-Type': 'application/fhir+json',
      'X-Requested-By': data?.audit?.createdBy,
      ...configs?.headers,
    };

    if (prefer) {
      headers['Prefer'] = prefer;
    }
    if (data?.audit) delete data?.audit; // xóa vì body  HC k cần

    const axiosRes = await this.httpClientService.post(FhirGroupPatientApiEndpoint.CREATE_GROUP_PATIENT, data, {
      ...configs,
      headers,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(FhirGroupPatientCreatedDto, axiosRes?.data || null) as FhirGroupPatientCreatedDto;
  }

  /**
   * Retrieves a Group resource by ID
   * @param id - The Group resource ID
   * @param configs - Optional HTTP client configuration
   * @returns Promise<FhirGroupPatientCreatedDto> - The Group resource
   */
  async getGroupById(id: string, configs?: HttpClientRequestConfig): Promise<FhirGroupPatientCreatedDto> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(FhirGroupPatientApiEndpoint.GET_GROUP_BY_ID, id),
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(FhirGroupPatientCreatedDto, axiosRes?.data || null);
  }

  /**
   * Updates an existing FHIR Group resource
   * @param data - The Group resource data to update
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<FhirGroupPatientCreatedDto> - Update result
   */
  async updateGroupPatient(
    data: FhirGroupPatientCreatedDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<FhirGroupPatientCreatedDto> {
    const headers: any = {
      'Content-Type': 'application/fhir+json',
      'X-Requested-By': data?.audit?.modifiedBy,
      ...configs?.headers,
    };

    if (prefer) {
      headers['Prefer'] = prefer;
    }
    if (data?.audit) delete data?.audit; // xóa vì body  HC k cần

    const axiosRes = await this.httpClientService.put(FhirGroupPatientApiEndpoint.UPDATE_GROUP_PATIENT, data, {
      ...configs,
      headers,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(FhirGroupPatientCreatedDto, axiosRes?.data || null) as FhirGroupPatientCreatedDto;
  }

  /**
   * Soft deletes a Group resource
   * @param id - The Group resource ID to delete
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<FhirGroupPatientCreatedDto> - Deletion result
   */
  async deleteGroupById(
    id: string,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<FhirGroupPatientCreatedDto> {
    const headers: any = {
      'Content-Type': 'application/fhir+json',
      ...configs?.headers,
    };

    if (prefer) {
      headers['Prefer'] = prefer;
    }

    const axiosRes = await this.httpClientService.delete(
      _interpolateString(FhirGroupPatientApiEndpoint.DELETE_GROUP_BY_ID, id),
      {
        ...configs,
        headers,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(FhirGroupPatientCreatedDto, axiosRes?.data || null) as FhirGroupPatientCreatedDto;
  }
}
