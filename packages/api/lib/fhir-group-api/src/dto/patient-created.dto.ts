import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { FhirGenderEnum, FhirResourceTypeEnum } from '../constants';
import {
  FhirAddressDto,
  FhirAttachmentDto,
  FhirCodeableConceptDto,
  FhirCommunicationDto,
  FhirContactDto,
  FhirContactPointDto,
  FhirExtensionDto,
  FhirHumanNameDto,
  FhirIdentifierDto,
  FhirLinkDto,
  FhirMetaDto,
  FhirReferenceDto,
} from './fhir-patient.dto';

// Patient DTO tổng hợp
export class FhirPatientCreatedDto {
  @ApiProperty({ description: 'Loại tài nguyên', enum: FhirResourceTypeEnum, default: FhirResourceTypeEnum.PATIENT })
  @Expose()
  resourceType?: FhirResourceTypeEnum;

  @ApiProperty({ description: 'ID bệnh nhân', required: false })
  @IsOptional()
  @Expose()
  id?: string;

  @ApiProperty({ description: 'Thông tin meta', required: false, type: FhirMetaDto })
  @IsOptional()
  @Type(() => FhirMetaDto)
  @Expose()
  meta?: FhirMetaDto;

  @ApiProperty({ description: 'Danh sách extension', required: false, type: FhirExtensionDto, isArray: true })
  @IsOptional()
  @Type(() => FhirExtensionDto)
  @Expose()
  extension?: FhirExtensionDto[];

  @ApiProperty({ description: 'Định danh', required: false, type: FhirIdentifierDto, isArray: true })
  @IsOptional()
  @Type(() => FhirIdentifierDto)
  @Expose()
  identifier?: FhirIdentifierDto[];

  @ApiProperty({ description: 'Trạng thái hoạt động', required: false })
  @IsOptional()
  @Expose()
  active?: boolean;

  @ApiProperty({ description: 'Tên bệnh nhân', required: false, type: FhirHumanNameDto, isArray: true })
  @IsOptional()
  @Type(() => FhirHumanNameDto)
  @Expose()
  name?: FhirHumanNameDto[];

  @ApiProperty({ description: 'Thông tin liên lạc', required: false, type: FhirContactPointDto, isArray: true })
  @IsOptional()
  @Type(() => FhirContactPointDto)
  @Expose()
  telecom?: FhirContactPointDto[];

  @ApiProperty({ description: 'Giới tính', required: false, enum: FhirGenderEnum, example: FhirGenderEnum.FEMALE })
  @IsOptional()
  @Expose()
  gender?: FhirGenderEnum;

  @ApiProperty({ description: 'Ngày sinh', required: false })
  @IsOptional()
  @Expose()
  birthDate?: string;

  @ApiProperty({ description: 'Đã qua đời', required: false })
  @IsOptional()
  @Expose()
  deceasedBoolean?: boolean;

  @ApiProperty({ description: 'Thời điểm qua đời', required: false })
  @IsOptional()
  @Expose()
  deceasedDateTime?: Date;

  @ApiProperty({ description: 'Địa chỉ', required: false, type: FhirAddressDto, isArray: true })
  @IsOptional()
  @Type(() => FhirAddressDto)
  @Expose()
  address?: FhirAddressDto[];

  @ApiProperty({ description: 'Tình trạng hôn nhân', required: false, type: FhirCodeableConceptDto })
  @IsOptional()
  @Type(() => FhirCodeableConceptDto)
  @Expose()
  maritalStatus?: FhirCodeableConceptDto;

  @ApiProperty({ description: 'Sinh đôi', required: false })
  @IsOptional()
  @Expose()
  multipleBirthBoolean?: boolean;

  @ApiProperty({ description: 'Thứ tự trong lần sinh nhiều', required: false })
  @IsOptional()
  @Expose()
  multipleBirthInteger?: Number;

  @ApiProperty({ description: 'Ảnh bệnh nhân', required: false, type: FhirAttachmentDto, isArray: true })
  @IsOptional()
  @Type(() => FhirAttachmentDto)
  @Expose()
  photo?: FhirAttachmentDto[];

  @ApiProperty({ description: 'Liên hệ', required: false, type: FhirContactDto, isArray: true })
  @IsOptional()
  @Type(() => FhirContactDto)
  @Expose()
  contact?: FhirContactDto[];

  @ApiProperty({ description: 'Ngôn ngữ giao tiếp', required: false, type: FhirCommunicationDto, isArray: true })
  @IsOptional()
  @Type(() => FhirCommunicationDto)
  @Expose()
  communication?: FhirCommunicationDto[];

  @ApiProperty({ description: 'Bác sĩ gia đình', required: false, type: FhirReferenceDto, isArray: true })
  @IsOptional()
  @Type(() => FhirReferenceDto)
  @Expose()
  generalPractitioner?: FhirReferenceDto[];

  @ApiProperty({ description: 'Tổ chức quản lý', required: false, type: FhirReferenceDto })
  @IsOptional()
  @Type(() => FhirReferenceDto)
  @Expose()
  managingOrganization?: FhirReferenceDto;

  @ApiProperty({ description: 'Liên kết', required: false, type: FhirLinkDto, isArray: true })
  @IsOptional()
  @Type(() => FhirLinkDto)
  @Expose()
  link?: FhirLinkDto[];
}
