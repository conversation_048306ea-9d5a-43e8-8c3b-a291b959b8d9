import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsOptional, IsString, IsArray, IsBoolean, IsDateString, ValidateNested } from 'class-validator';
import {
  FhirAddressType,
  FhirAddressUse,
  FhirGenderEnum,
  FhirHumanNameUse,
  FhirLinkTypeEnum,
  FhirRecordStageEnum,
  FhirTelecomSystem,
  FhirTelecomUse,
} from '../constants';

/**
 * FHIR Identifier DTO
 */
export class FhirIdentifierDto {
  @IsOptional()
  @IsString()
  @Expose()
  use?: string;

  @IsOptional()
  @Expose()
  type?: any;

  @IsOptional()
  @IsString()
  @Expose()
  system?: string;

  @IsOptional()
  @IsString()
  @Expose()
  value?: string;
}

/**
 * FHIR HumanName DTO
 */
export class FhirHumanNameDto {
  @ApiProperty({ enum: FhirHumanNameUse, required: false, example: FhirHumanNameUse.OFFICIAL })
  @IsOptional()
  @IsString()
  @Expose()
  use?: FhirHumanNameUse;

  @IsOptional()
  @IsString()
  @Expose()
  text?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Expose()
  family?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Expose()
  given?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Expose()
  prefix?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Expose()
  suffix?: string[];
}

/**
 * FHIR ContactPoint DTO
 */
export class FhirContactPointDto {
  @ApiProperty({ enum: FhirTelecomSystem, required: false, example: FhirTelecomSystem.PHONE })
  @IsOptional()
  @IsString()
  @Expose()
  system?: FhirTelecomSystem;

  @IsOptional()
  @IsString()
  @Expose()
  value?: string;

  @ApiProperty({ enum: FhirTelecomUse, required: false, example: FhirTelecomUse.HOME })
  @IsOptional()
  @IsString()
  @Expose()
  use?: FhirTelecomUse;

  @IsOptional()
  @Expose()
  rank?: number;
}

/**
 * FHIR Address DTO
 */
export class FhirAddressDto {
  @ApiProperty({ enum: FhirAddressUse, required: false, example: FhirAddressUse.HOME })
  @IsOptional()
  @IsString()
  @Expose()
  use?: FhirAddressUse;

  @ApiProperty({ enum: FhirAddressType, required: false, example: FhirAddressType.BOTH })
  @IsOptional()
  @IsString()
  @Expose()
  type?: FhirAddressType;

  @IsOptional()
  @IsString()
  @Expose()
  text?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Expose()
  line?: string[];

  @IsOptional()
  @IsString()
  @Expose()
  city?: string;

  @IsOptional()
  @IsString()
  @Expose()
  district?: string;

  @IsOptional()
  @IsString()
  @Expose()
  state?: string;

  @IsOptional()
  @IsString()
  @Expose()
  postalCode?: string;

  @IsOptional()
  @IsString()
  @Expose()
  country?: string;
}

/**
 * FHIR Patient DTO
 * Based on FHIR R5 Patient resource structure
 */
export class FhirPatientDto {
  @IsOptional()
  @IsString()
  @Expose()
  resourceType?: string = 'Patient';

  @IsOptional()
  @IsString()
  @Expose()
  id?: string;

  @IsOptional()
  @Expose()
  meta?: any;

  @IsOptional()
  @IsString()
  @Expose()
  implicitRules?: string;

  @IsOptional()
  @IsString()
  @Expose()
  language?: string;

  @IsOptional()
  @Expose()
  text?: any;

  @IsOptional()
  @IsArray()
  @Expose()
  contained?: any[];

  @IsOptional()
  @IsArray()
  @Expose()
  extension?: any[];

  @IsOptional()
  @IsArray()
  @Expose()
  modifierExtension?: any[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FhirIdentifierDto)
  @Expose()
  identifier?: FhirIdentifierDto[];

  @IsOptional()
  @IsBoolean()
  @Expose()
  active?: boolean;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FhirHumanNameDto)
  @Expose()
  name?: FhirHumanNameDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FhirContactPointDto)
  @Expose()
  telecom?: FhirContactPointDto[];

  @IsOptional()
  @IsString()
  @Expose()
  gender?: string;

  @IsOptional()
  @IsDateString()
  @Expose()
  birthDate?: string;

  @IsOptional()
  @Expose()
  deceasedBoolean?: boolean;

  @IsOptional()
  @IsDateString()
  @Expose()
  deceasedDateTime?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FhirAddressDto)
  @Expose()
  address?: FhirAddressDto[];

  @IsOptional()
  @Expose()
  maritalStatus?: any;

  @IsOptional()
  @Expose()
  multipleBirthBoolean?: boolean;

  @IsOptional()
  @Expose()
  multipleBirthInteger?: number;

  @IsOptional()
  @IsArray()
  @Expose()
  photo?: any[];

  @IsOptional()
  @IsArray()
  @Expose()
  contact?: any[];

  @IsOptional()
  @IsArray()
  @Expose()
  communication?: any[];

  @IsOptional()
  @IsArray()
  @Expose()
  generalPractitioner?: any[];

  @IsOptional()
  @Expose()
  managingOrganization?: any;

  @IsOptional()
  @IsArray()
  @Expose()
  link?: any[];
}

export class FhirMetaDto {
  @ApiProperty({ description: 'Thời điểm cập nhật cuối cùng', required: false, default: new Date().toISOString() })
  @IsOptional()
  @Expose()
  lastUpdated?: string;

  @ApiProperty({ description: 'Nguồn dữ liệu', required: false })
  @IsOptional()
  @Expose()
  source?: string;
}

export class FhirExtensionDto {
  @ApiProperty({
    description: 'Đường dẫn URL định nghĩa extension',
    required: true,
    example: 'http://example.com/fhir/StructureDefinition/record-stage',
  })
  @Expose()
  @IsOptional()
  url?: string;

  @ApiProperty({
    description: 'Giá trị code của extension',
    required: false,
    example: FhirRecordStageEnum.CREATED,
    enum: FhirRecordStageEnum,
  })
  @IsOptional()
  @Expose()
  valueCode?: string;
}

export class FhirCodingDto {
  @ApiProperty({ description: 'Hệ thống mã hóa', required: false })
  @IsOptional()
  @Expose()
  system?: string;

  @ApiProperty({ description: 'Mã code', required: false })
  @IsOptional()
  @Expose()
  code?: string;

  @ApiProperty({ description: 'Tên hiển thị', required: false })
  @IsOptional()
  @Expose()
  display?: string;
}
export class FhirCodeableConceptDto {
  @ApiProperty({ description: 'Danh sách coding', required: false, type: FhirCodingDto, isArray: true })
  @IsOptional()
  @Type(() => FhirCodingDto)
  @Expose()
  coding?: FhirCodingDto[];

  @ApiProperty({ description: 'Text hiển thị', required: false })
  @IsOptional()
  @Expose()
  text?: string;
}

export class FhirAttachmentDto {
  @ApiProperty({ description: 'Kiểu nội dung ảnh (ví dụ: image/jpeg)', required: false })
  @IsOptional()
  @Expose()
  contentType?: string;

  @ApiProperty({ description: 'Đường dẫn ảnh', required: false })
  @IsOptional()
  @Expose()
  url?: string;

  @ApiProperty({ description: 'Tiêu đề ảnh', required: false })
  @IsOptional()
  @Expose()
  title?: string;
}

export class FhirPeriodDto {
  @ApiProperty({ description: 'Ngày bắt đầu', required: false, type: String, example: '2025-01-01' })
  @IsOptional()
  @Expose()
  start?: string;

  @ApiProperty({ description: 'Ngày kết thúc', required: false, type: String, example: '2025-12-31' })
  @IsOptional()
  @Expose()
  end?: string;
}

export class FhirContactDto {
  @ApiProperty({
    description: 'Mối quan hệ với bệnh nhân',
    required: false,
    type: FhirCodeableConceptDto,
    isArray: true,
  })
  @IsOptional()
  @Type(() => FhirCodeableConceptDto)
  @Expose()
  relationship?: FhirCodeableConceptDto[];

  @ApiProperty({ description: 'Tên của người liên lạc', required: false, type: FhirHumanNameDto })
  @IsOptional()
  @Type(() => FhirHumanNameDto)
  @Expose()
  name?: FhirHumanNameDto;

  @ApiProperty({ description: 'Phương thức liên lạc', required: false, type: FhirContactPointDto, isArray: true })
  @IsOptional()
  @Type(() => FhirContactPointDto)
  @Expose()
  telecom?: FhirContactPointDto[];

  @ApiProperty({ description: 'Địa chỉ', required: false, type: FhirAddressDto })
  @IsOptional()
  @Type(() => FhirAddressDto)
  @Expose()
  address?: FhirAddressDto;

  @ApiProperty({ description: 'Giới tính', required: false, enum: FhirGenderEnum, example: FhirGenderEnum.FEMALE })
  @IsOptional()
  @Expose()
  gender?: FhirGenderEnum;

  @ApiProperty({ description: 'Tổ chức liên quan', required: false })
  @IsOptional()
  @Expose()
  organization?: any;

  @ApiProperty({ description: 'Thời gian quan hệ có hiệu lực', required: false, type: FhirPeriodDto })
  @IsOptional()
  @Expose()
  period?: FhirPeriodDto;
}

export class FhirCommunicationDto {
  @ApiProperty({ description: 'Ngôn ngữ giao tiếp', required: false, type: FhirCodeableConceptDto })
  @IsOptional()
  @Type(() => FhirCodeableConceptDto)
  @Expose()
  language?: FhirCodeableConceptDto;

  @ApiProperty({ description: 'Có phải ngôn ngữ ưu tiên', required: false })
  @IsOptional()
  @IsBoolean()
  @Expose()
  preferred?: boolean;
}

export class FhirReferenceDto {
  @ApiProperty({ description: 'Tham chiếu đến tài nguyên khác', required: false, example: 'Practitioner/doc-001' })
  @IsOptional()
  @Expose()
  reference?: string;

  @ApiProperty({ description: 'Tên hiển thị', required: false, example: 'Dr. John Smith' })
  @IsOptional()
  @Expose()
  display?: string;
}

export class FhirLinkDto {
  @ApiProperty({ description: 'Tham chiếu đến đối tượng khác', required: false, type: FhirReferenceDto })
  @IsOptional()
  @Type(() => FhirReferenceDto)
  @Expose()
  other?: FhirReferenceDto;

  @ApiProperty({
    description: 'Loại liên kết',
    required: false,
    enum: FhirLinkTypeEnum,
    example: FhirLinkTypeEnum.REFER,
  })
  @IsOptional()
  @Expose()
  type?: FhirLinkTypeEnum;
}

export class FhirGroupMemberDto {
  @ApiProperty({
    description: 'Tham chiếu đến entity (Patient hoặc RelatedPerson)',
    required: true,
    type: FhirReferenceDto,
  })
  @Type(() => FhirReferenceDto)
  @Expose()
  entity: FhirReferenceDto;

  @ApiProperty({ description: 'Khoảng thời gian là thành viên', required: false, type: FhirPeriodDto })
  @IsOptional()
  @Type(() => FhirPeriodDto)
  @Expose()
  period?: FhirPeriodDto;

  @ApiProperty({ description: 'Có không còn là thành viên không?', required: false })
  @IsOptional()
  @IsBoolean()
  @Expose()
  inactive?: boolean;
}
