import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MAX_REDIRECT, TIMEOUT } from '../../constants';
import { HttpClientModule } from '../../http-client/src';
import { FhirGroupPatientApiService } from './fhir-group-api.service';

/**
 * FHIR Patient API Module
 * Provides FHIR R5 Patient resource operations
 */
@Module({
  imports: [
    HttpClientModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        baseURL: configService.get('FHIR_GROUP_PATIENT_SERVER_URL'),
        timeout: TIMEOUT,
        maxRedirects: MAX_REDIRECT,
        validateStatus: () => {
          return true;
        },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [FhirGroupPatientApiService],
  exports: [FhirGroupPatientApiService],
})
export class FhirGroupPatientApiModule {}
