import { Injectable } from '@nestjs/common';
import { HttpClientRequestConfig } from '../../http-client/src';
import { FhirGroupPatientCreatedDto } from './dto';

/**
 * Abstract class defining FHIR Patient API operations
 * All FHIR Patient service implementations must extend this class
 */
@Injectable()
export abstract class FhirGroupPatientApiAbstract {
  /**
   * Tạo mới một Group Patient.
   * @param dto Dữ liệu Group cần tạo
   * @param configs Tuỳ chọn cấu hình cho request/service
   * @returns Group đã được tạo
   */
  abstract createGroupPatient(
    body: FhirGroupPatientCreatedDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<FhirGroupPatientCreatedDto>;

  /**
   * Retrieves a Group resource by ID
   * @param id - The Group resource ID
   * @param configs - Optional HTTP client configuration
   * @returns Promise<JsonNode> - The Group resource
   */
  abstract getGroupById(id: string, configs?: HttpClientRequestConfig): Promise<FhirGroupPatientCreatedDto>;

  /**
   * Updates an existing FHIR Group resource
   * @param data - The Group resource data to update
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<FhirGroupPatientCreatedDto> - Update result
   */
  abstract updateGroupPatient(
    data: FhirGroupPatientCreatedDto,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<FhirGroupPatientCreatedDto>;

  /**
   * Soft deletes a Group resource
   * @param id - The Group resource ID to delete
   * @param prefer - Optional Prefer header value
   * @param configs - Optional HTTP client configuration
   * @returns Promise<FhirGroupPatientCreatedDto> - Deletion result
   */
  abstract deleteGroupById(
    id: string,
    prefer?: string,
    configs?: HttpClientRequestConfig,
  ): Promise<FhirGroupPatientCreatedDto>;
}
