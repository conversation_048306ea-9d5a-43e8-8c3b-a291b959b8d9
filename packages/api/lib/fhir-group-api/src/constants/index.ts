export enum FhirResourceTypeEnum {
  PATIENT = 'Patient',
  RELATED_PERSON = 'RelatedPerson',
  GROUP = 'Group',
}

export enum FhirRecordStageEnum {
  CREATED = 'created',
  UPDATED = 'updated',
  CONFIRMED = 'confirmed',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum FhirGenderEnum {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
  UNKNOWN = 'unknown',
}

export enum FhirLinkTypeEnum {
  REPLACED_BY = 'replaced-by',
  REPLACES = 'replaces',
  REFER = 'refer',
  SEEALSO = 'seealso',
}

export enum FhirHumanNameUse {
  USUAL = 'usual',
  OFFICIAL = 'official',
  TEMP = 'temp',
  NICKNAME = 'nickname',
  ANONYMOUS = 'anonymous',
  OLD = 'old',
  MAIDEN = 'maiden',
}

export enum FhirAddressUse {
  HOME = 'home',
  WORK = 'work',
  TEMP = 'temp',
  OLD = 'old',
  BILLING = 'billing',
}

export enum FhirAddressType {
  POSTAL = 'postal',
  PHYSICAL = 'physical',
  BOTH = 'both',
}

export enum FhirTelecomSystem {
  PHONE = 'phone',
  FAX = 'fax',
  EMAIL = 'email',
  PAGER = 'pager',
  URL = 'url',
  SMS = 'sms',
  OTHER = 'other',
}

export enum FhirTelecomUse {
  HOME = 'home',
  WORK = 'work',
  TEMP = 'temp',
  OLD = 'old',
  MOBILE = 'mobile',
}

export const FhirVaccineSourceURN = {
  0: 'urn:vaccine:source:rsa',
  1: 'urn:vaccine:source:stc',
  2: 'urn:vaccine:source:rsa-ecom',
  3: 'urn:vaccine:source:web-rsa-affiliate',
  4: 'urn:vaccine:source:app-mrsa-affiliate',
} as const;

export type FhirVaccineSourceType = keyof typeof FhirVaccineSourceURN;
