import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsString, IsNumber, IsBoolean, IsOptional, IsDate, IsArray, ValidateNested } from 'class-validator';

export class GetTicketByIdDto {
  @ApiProperty({ required: true })
  @IsString()
  @Expose()
  ticketCode: string;
}

export class HealthCheckQuestionDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  ticketId?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  stt?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  questionId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  questionContext?: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  @Expose()
  answer?: boolean;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  note?: string;

  @ApiProperty()
  @IsDate()
  @IsOptional()
  @Expose()
  createdDate?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @IsDate()
  @IsOptional()
  @Expose()
  modifiedDate?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  modifiedBy?: string;
}

export class HealthCheckDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  ticketId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  biosignalId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  biosignalValue?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  fieldName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  doctorNote?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  stt?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  displayName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  unit?: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  @Expose()
  isRequired?: boolean;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  inputType?: string;

  @ApiProperty()
  @IsDate()
  @IsOptional()
  @Expose()
  createdDate?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @IsDate()
  @IsOptional()
  @Expose()
  modifiedDate?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  modifiedBy?: string;
}

export class TicketImageDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  ticketId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  image?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  type?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  createdDate?: any;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  modifiedBy?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  modifiedDate?: any;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  url?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  orderDetailAttachmentCode?: string;
}

export class IndicationResDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  sourceId?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  diseaseGroupId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  ticketId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  orderDetailAttachmentCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  sku?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  vaccineName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  taxonomies?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  origin?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  manufactor?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  orderInjections?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  dosage?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  injectionRoute?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  position?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  lotNumber?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  lotDate?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  note?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  status?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  orderInjectionId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  orderCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  orderCodeCounter?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  regimenId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  regimenName?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  unitCode?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  unitName?: string;

  @ApiProperty()
  @IsDate()
  @IsOptional()
  @Expose()
  createdDate?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @IsDate()
  @IsOptional()
  @Expose()
  modifiedDate?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  modifiedBy?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  orderDetailAttachmentId?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  requireBeforeInjection?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  declarationId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  productItemCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  orderReturnCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  orderReturnId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  barCode?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  unitCodeSale?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  unitNameSale?: string;

  @ApiProperty({ type: [TicketImageDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TicketImageDto)
  @IsOptional()
  @Expose()
  image?: TicketImageDto[];
}

export class ScheduleResDto {
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  partialPaid?: number;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  @Expose()
  doctorRegimen?: boolean;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  @Expose()
  isClosedRegimen?: boolean;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  sourceId?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  orderDetailAttachmentCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  diseaseGroupId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  ticketCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  regimenId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  regimenName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  sku?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  vaccineName?: string;

  @ApiProperty()
  @IsDate()
  @IsOptional()
  @Expose()
  appointmentDate?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  manufactor?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  dosage?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  injectionRoute?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  orderInjections?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  vaccinatedNow?: string;

  @ApiProperty()
  @IsDate()
  @IsOptional()
  @Expose()
  createdDate?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  taxonomies?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  status?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  orderInjectionId?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  orderDetailAttachmentId?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  requireBeforeInjection?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  unitCode?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  unitName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  scheduleType?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  maxInjections?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  requiredInjections?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  unitCodeSale?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  unitNameSale?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  ticketCodeOld?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  orderCodeOld?: string;
}

export class MaterialAttachmentDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  ticketId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  sku?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  skuName?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  quantity?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  unitCode?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  unitName?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  unitLevel?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  whsCode?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  materialType?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  orderCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  image?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  status?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @IsDate()
  @IsOptional()
  @Expose()
  createdDate?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  modifiedBy?: string;

  @ApiProperty()
  @IsDate()
  @IsOptional()
  @Expose()
  modifiedDate?: Date;
}

export class ReactionAfterInjectionDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  ticketCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  note?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  monitorTypeName?: string;

  @ApiProperty({ type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Expose()
  details?: string[];
}

export class TicketPropertyDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  ticketId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  lcvId?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  code?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  name?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  value?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  sku?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  regimenId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  note?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @IsDate()
  @IsOptional()
  @Expose()
  createdDate?: Date;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  modifiedBy?: string;

  @ApiProperty()
  @IsDate()
  @IsOptional()
  @Expose()
  modifiedDate?: Date;
}

export class GetTicketByIdRes {
  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  paymentEmployeeCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  paymentEmployeeName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  ticketCodeOld?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  orderCodeOld?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  ticketCode?: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  @Expose()
  pauseInjections?: boolean;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  noNumber?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  shopCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  shopName?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  roomType?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  clinicId?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  injectionClinicId?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  personId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  lcvId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  clinicName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  injectionClinicName?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  tableId?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  status?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  height?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  weight?: number;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  @Expose()
  isPriority?: boolean;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  note?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  trackingTime?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  completedTrackingTime?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  doctorCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  doctorName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  injectingNursingCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  injectingNursingName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  supervisedNursingCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  supervisedNursingName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  orderCode?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  ticketType?: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  paymentType?: number;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  @Expose()
  isReExamination?: boolean;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  conclusion?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  sessionId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  createdDate?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  source?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  saleEcomNote?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  saleShopNote?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  custNoteOnline?: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Expose()
  ticketVersion?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  modifiedByName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  modifiedBy?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  linkId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  examinedTime?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  journeyId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  personName?: string;

  @ApiProperty({ type: [HealthCheckQuestionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => HealthCheckQuestionDto)
  @IsOptional()
  @Expose()
  healthCheckQuestions?: HealthCheckQuestionDto[];

  @ApiProperty({ type: [HealthCheckDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => HealthCheckDto)
  @IsOptional()
  @Expose()
  healthChecks?: HealthCheckDto[];

  @ApiProperty({ type: [IndicationResDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IndicationResDto)
  @IsOptional()
  @Expose()
  indications?: IndicationResDto[];

  @ApiProperty({ type: [ScheduleResDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ScheduleResDto)
  @IsOptional()
  @Expose()
  schedules?: ScheduleResDto[];

  @ApiProperty({ type: [MaterialAttachmentDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MaterialAttachmentDto)
  @IsOptional()
  @Expose()
  materialAttachments?: MaterialAttachmentDto[];

  @ApiProperty({ type: [TicketImageDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TicketImageDto)
  @IsOptional()
  @Expose()
  image?: TicketImageDto[];

  @ApiProperty({ type: [ReactionAfterInjectionDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ReactionAfterInjectionDto)
  @IsOptional()
  @Expose()
  reactionAfterInjection?: ReactionAfterInjectionDto[];

  @ApiProperty({ type: [TicketPropertyDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TicketPropertyDto)
  @IsOptional()
  @Expose()
  ticketProperties?: TicketPropertyDto[];
}
