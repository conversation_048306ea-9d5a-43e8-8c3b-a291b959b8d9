import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsString, IsOptional, IsArray, ValidateNested } from 'class-validator';

export class GetHealthMonitorDto {
  @ApiProperty({ required: true })
  @IsString()
  @Expose()
  ticketCode: string;
}

export class HealthMonitorDetailDto {
  @ApiProperty()
  @IsOptional()
  @Expose()
  detail?: any;
}

export class HealthMonitorItemDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  ticketCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  note?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  monitorTypeName?: string;

  @ApiProperty({ type: [HealthMonitorDetailDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => HealthMonitorDetailDto)
  @IsOptional()
  @Expose()
  details?: HealthMonitorDetailDto[];
}

export class HealthMonitorDataDto {
  @ApiProperty({ type: [HealthMonitorItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => HealthMonitorItemDto)
  @IsOptional()
  @Expose()
  items?: HealthMonitorItemDto[];
}

export class GetHealthMonitorRes {
  @ApiProperty()
  @IsOptional()
  @Expose()
  status?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  message?: string;

  @ApiProperty({ type: HealthMonitorDataDto })
  @ValidateNested()
  @Type(() => HealthMonitorDataDto)
  @IsOptional()
  @Expose()
  data?: HealthMonitorDataDto;
}
