import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsOptional, IsNumber, IsString, IsArray } from 'class-validator';

export class SearchTicketWithImageOnlyDto {
  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  @IsNumber()
  skipCount?: number;

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  @IsString()
  fromDate?: string;

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  @IsString()
  toDate?: string;

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  @IsString()
  keyWord?: string;

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  @IsString()
  shopCode?: string;

  @ApiProperty({ required: false, type: [Number] })
  @Expose()
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  ticketType?: number[];

  @ApiProperty({ required: false, type: [String] })
  @Expose()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  doctorCode?: string[];

  @ApiProperty({ required: false, type: [String] })
  @Expose()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  injectingNursingCode?: string[];

  @ApiProperty({ required: false, type: [String] })
  @Expose()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  supervisedNursingCode?: string[];

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  @IsNumber()
  roomType?: number;

  @ApiProperty({ required: false, type: [Number] })
  @Expose()
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  statuses?: number[];

  @ApiProperty({ required: false, type: [Number] })
  @Expose()
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  clinicIds?: number[];

  @ApiProperty({ required: false, type: [String] })
  @Expose()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  personIds?: string[];

  @ApiProperty({ required: false, type: [Number] })
  @Expose()
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  injectionClinicIds?: number[];

  @ApiProperty({ required: false, type: [String] })
  @Expose()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  skus?: string[];

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  @IsNumber()
  maxResultCount?: number;
}

export class TicketAttachmentDto {
  @ApiProperty()
  @Expose()
  id?: string;

  @ApiProperty()
  @Expose()
  ticketId?: string;

  @ApiProperty()
  @Expose()
  image?: string;

  @ApiProperty()
  @Expose()
  type?: number;

  @ApiProperty()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @Expose()
  createdDate?: string;

  @ApiProperty()
  @Expose()
  modifiedBy?: string;

  @ApiProperty()
  @Expose()
  modifiedDate?: string;

  @ApiProperty()
  @Expose()
  orderDetailAttachmentCode?: string;

  @ApiProperty()
  @Expose()
  signName?: string;
}

export class TicketIndicationDto {
  @ApiProperty()
  @Expose()
  id?: string;

  @ApiProperty()
  @Expose()
  ticketId?: string;

  @ApiProperty()
  @Expose()
  sku?: string;

  @ApiProperty()
  @Expose()
  vaccineName?: string;

  @ApiProperty()
  @Expose()
  taxonomies?: string;

  @ApiProperty()
  @Expose()
  origin?: string;

  @ApiProperty()
  @Expose()
  manufactor?: string;

  @ApiProperty()
  @Expose()
  orderInjections?: number;

  @ApiProperty()
  @Expose()
  dosage?: string;

  @ApiProperty()
  @Expose()
  injectionRoute?: string;

  @ApiProperty()
  @Expose()
  position?: string;

  @ApiProperty()
  @Expose()
  lotDate?: string;

  @ApiProperty()
  @Expose()
  lotNumber?: string;

  @ApiProperty()
  @Expose()
  status?: number;

  @ApiProperty()
  @Expose()
  note?: string;

  @ApiProperty()
  @Expose()
  orderInjectionId?: string;

  @ApiProperty()
  @Expose()
  orderCode?: string;

  @ApiProperty()
  @Expose()
  orderCodeCounter?: string;

  @ApiProperty()
  @Expose()
  regimenId?: string;

  @ApiProperty()
  @Expose()
  regimenName?: string;

  @ApiProperty()
  @Expose()
  unitCode?: number;

  @ApiProperty()
  @Expose()
  unitName?: string;

  @ApiProperty()
  @Expose()
  orderDetailAttachmentId?: number;

  @ApiProperty()
  @Expose()
  orderDetailAttachmentCode?: string;

  @ApiProperty()
  @Expose()
  requireBeforeInjection?: number;

  @ApiProperty()
  @Expose()
  declarationId?: string;

  @ApiProperty()
  @Expose()
  productItemCode?: string;

  @ApiProperty()
  @Expose()
  orderReturnId?: string;

  @ApiProperty()
  @Expose()
  orderReturnCode?: string;

  @ApiProperty()
  @Expose()
  barCode?: string;

  @ApiProperty()
  @Expose()
  unitCodeSale?: number;

  @ApiProperty()
  @Expose()
  unitNameSale?: string;

  @ApiProperty()
  @Expose()
  createdDate?: string;

  @ApiProperty()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @Expose()
  modifiedDate?: string;

  @ApiProperty()
  @Expose()
  modifiedBy?: string;
}

export class SearchTicketItemDto {
  @ApiProperty()
  @Expose()
  id?: string;

  @ApiProperty()
  @Expose()
  ticketCode?: string;

  @ApiProperty()
  @Expose()
  ticketType?: number;

  @ApiProperty()
  @Expose()
  noNumber?: number;

  @ApiProperty()
  @Expose()
  shopCode?: string;

  @ApiProperty()
  @Expose()
  shopName?: string;

  @ApiProperty()
  @Expose()
  roomType?: number;

  @ApiProperty()
  @Expose()
  clinicId?: number;

  @ApiProperty()
  @Expose()
  injectionClinicId?: number;

  @ApiProperty()
  @Expose()
  personId?: string;

  @ApiProperty()
  @Expose()
  lcvId?: string;

  @ApiProperty()
  @Expose()
  clinicName?: string;

  @ApiProperty()
  @Expose()
  injectionClinicName?: string;

  @ApiProperty()
  @Expose()
  status?: number;

  @ApiProperty()
  @Expose()
  isPriority?: boolean;

  @ApiProperty()
  @Expose()
  note?: string;

  @ApiProperty()
  @Expose()
  examinedTime?: string;

  @ApiProperty()
  @Expose()
  trackingTime?: string;

  @ApiProperty()
  @Expose()
  completedTrackingTime?: string;

  @ApiProperty()
  @Expose()
  doctorCode?: string;

  @ApiProperty()
  @Expose()
  doctorName?: string;

  @ApiProperty()
  @Expose()
  injectingNursingCode?: string;

  @ApiProperty()
  @Expose()
  injectingNursingName?: string;

  @ApiProperty()
  @Expose()
  supervisedNursingCode?: string;

  @ApiProperty()
  @Expose()
  supervisedNursingName?: string;

  @ApiProperty()
  @Expose()
  orderCode?: string;

  @ApiProperty()
  @Expose()
  paymentType?: number;

  @ApiProperty()
  @Expose()
  isReExamination?: boolean;

  @ApiProperty()
  @Expose()
  sessionId?: string;

  @ApiProperty()
  @Expose()
  journeyId?: string;

  @ApiProperty({ type: [TicketIndicationDto] })
  @Expose()
  @Type(() => TicketIndicationDto)
  indications?: TicketIndicationDto[];

  @ApiProperty()
  @Expose()
  createdDate?: string;

  @ApiProperty()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @Expose()
  createdByName?: string;

  @ApiProperty({ type: [TicketAttachmentDto] })
  @Expose()
  @Type(() => TicketAttachmentDto)
  image?: TicketAttachmentDto[];
}

export class SearchTicketWithImageOnlyRes {
  @ApiProperty({ type: [SearchTicketItemDto] })
  @Expose()
  @Type(() => SearchTicketItemDto)
  items?: SearchTicketItemDto[];

  @ApiProperty()
  @Expose()
  totalCount?: number;
}
