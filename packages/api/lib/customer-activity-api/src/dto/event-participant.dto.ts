import { ApiProperty, IntersectionType } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { MetaDto, PaginationBaseDto } from './common.dto';
//#region DTO data item base
export class EventParticipantBaseDto {
  @ApiProperty({ description: 'Event ID', example: 'evt_001' })
  @IsString()
  event_id: string;

  @ApiProperty({ description: 'User Phone Number', example: '0987654321' })
  @IsString()
  phone_number: string;

  @ApiProperty({ description: 'User Name', example: '<PERSON><PERSON><PERSON>' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'obtainable_event_ticket_quota', example: '1' })
  @IsInt()
  obtainable_event_ticket_quota?: number;
}

export class ItemEventParticipantDto extends IntersectionType(EventParticipantBaseDto) {
  @ApiProperty({ example: 'abc123', description: 'Unique item identifier' })
  id: string;

  @ApiProperty({ example: 'doc456', description: 'Document identifier related to the item' })
  documentId: string;

  @ApiProperty({ example: '2025-07-18T00:00:00.000Z', description: 'Created timestamp (ISO 8601)' })
  createdAt: string;

  @ApiProperty({ example: '2025-07-19T00:00:00.000Z', description: 'Last updated timestamp (ISO 8601)' })
  updatedAt: string;

  @ApiProperty({ example: '2025-07-20T00:00:00.000Z', description: 'Published timestamp (ISO 8601)' })
  publishedAt: string;
}
//#endregion DTO data item base

//#region DTO Post
export class PostEventParticipantPayloadDto extends IntersectionType(EventParticipantBaseDto) {}

export class PostEventParticipantResponseDto extends EventParticipantBaseDto {}

//#endregion DTO Post

//#region DTO Get list
export class GetListEventParticipantParamDto {
  @ApiProperty({ type: () => PaginationBaseDto, required: false })
  @ValidateNested()
  @Type(() => PaginationBaseDto)
  @IsOptional()
  pagination?: PaginationBaseDto;
}
export class GetListEventParticipantResponseDto {
  @ApiProperty({
    type: ItemEventParticipantDto,
    isArray: true,
    description: 'Danh sách item dữ liệu theo phân trang',
  })
  @ValidateNested({ each: true })
  @Type(() => ItemEventParticipantDto)
  data: ItemEventParticipantDto[];

  @ApiProperty({
    type: MetaDto,
    description: 'Thông tin phân trang',
  })
  @ValidateNested()
  @Type(() => MetaDto)
  meta: MetaDto;
}
//#endregion DTO Get list

//#region DTO Get by id
// export class GetEventParticipantParamDto {
//   @ApiProperty({ description: 'id', example: '1', required: true })
//   @IsInt()
//   id: number;
// }
export class GetEventParticipantResponseDto {
  @ApiProperty({
    type: ItemEventParticipantDto,
    description: 'item dữ liệu Event Participant',
  })
  @ValidateNested()
  @Type(() => ItemEventParticipantDto)
  data: ItemEventParticipantDto;
}
//#endregion DTO Get by id

//#region get remainingItemQuota
export class RemainingItemQuotaDto {
  @IsString()
  name: string;

  @IsBoolean()
  activity_finished: boolean;

  @IsNumber()
  remaining_event_ticket_quota: number;
}
//#endregion get remainingItemQuota
