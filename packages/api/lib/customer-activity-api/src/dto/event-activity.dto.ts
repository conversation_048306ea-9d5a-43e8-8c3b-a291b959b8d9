import { ApiProperty, IntersectionType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsBoolean, IsEmail, IsEnum, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { ActivityType } from '../constants/app.constant';
import { MetaDto } from './common.dto';
//#region DTO data item base
export class EventActivityStatusBaseDto {
  @ApiProperty({ description: 'Identify Status', example: true, required: false })
  @IsBoolean()
  @IsOptional()
  identify_status?: boolean;

  @ApiProperty({ description: 'Identify Data', example: {}, required: false })
  @IsOptional()
  identify_data?: any;

  @ApiProperty({ description: 'Identify Error', example: {}, required: false })
  @IsOptional()
  identify_error?: any;

  @ApiProperty({ description: 'Received Item Status', example: true, required: false })
  @IsBoolean()
  @IsOptional()
  received_item_status?: boolean;

  @ApiProperty({ description: 'Received Item Data', example: {}, required: false })
  @IsOptional()
  received_item_data?: any;

  @ApiProperty({ description: 'Received Item Error', example: {}, required: false })
  @IsOptional()
  received_item_error?: any;

  @ApiProperty({ description: 'Request_consultant Status', example: true, required: false })
  @IsBoolean()
  @IsOptional()
  request_consultant_status?: boolean;

  @ApiProperty({ description: 'Request_consultant Data', example: {}, required: false })
  @IsOptional()
  request_consultant_data?: any;

  @ApiProperty({ description: 'Request_consultant Error', example: {}, required: false })
  @IsOptional()
  request_consultant_error?: any;

  @ApiProperty({ description: 'conduct_survey Status', example: true, required: false })
  @IsBoolean()
  @IsOptional()
  conduct_survey_status?: boolean;

  @ApiProperty({ description: 'conduct_survey Data', example: {}, required: false })
  @IsOptional()
  conduct_survey_data?: any;

  @ApiProperty({ description: 'conduct_survey Error', example: {}, required: false })
  @IsOptional()
  conduct_survey_error?: any;
}
export class EventActivityBaseDto {
  @ApiProperty({ description: 'Event ID', example: 'evt_001' })
  @IsString()
  event_id: string;

  @ApiProperty({ description: 'Event Slug', example: 'event-2025' })
  @IsString()
  @IsOptional()
  event_slug?: string;

  @ApiProperty({ description: 'Event Name', example: 'My Event' })
  @IsString()
  @IsOptional()
  event_name?: string;

  @ApiProperty({ description: 'Company Name', example: 'Công ty A' })
  @IsString()
  @IsOptional()
  company_name?: string;

  @ApiProperty({ description: 'User Phone Number', example: '0987654321' })
  @IsString()
  user_phone_number: string;

  @ApiProperty({ description: 'User Name', example: 'Nguyen Van A' })
  @IsString()
  @IsOptional()
  user_name?: string;

  @ApiProperty({ description: 'User Email', example: '<EMAIL>' })
  @IsEmail()
  @IsOptional()
  user_email?: string;

  @ApiProperty({
    description: 'Activity Type ',
    enum: ActivityType,
    example: ActivityType.RECEIVE_ITEM,
  })
  @IsEnum(ActivityType)
  activity_type: ActivityType;
}

export class ItemEventActivityDto extends IntersectionType(EventActivityBaseDto, EventActivityStatusBaseDto) {
  @ApiProperty({ example: 'abc123', description: 'Unique item identifier' })
  id: string;

  @ApiProperty({ example: 'doc456', description: 'Document identifier related to the item' })
  documentId: string;

  @ApiProperty({ example: '2025-07-18T00:00:00.000Z', description: 'Created timestamp (ISO 8601)' })
  createdAt: string;

  @ApiProperty({ example: '2025-07-19T00:00:00.000Z', description: 'Last updated timestamp (ISO 8601)' })
  updatedAt: string;

  @ApiProperty({ example: '2025-07-20T00:00:00.000Z', description: 'Published timestamp (ISO 8601)' })
  publishedAt: string;
}
//#endregion DTO data item base

//#region DTO Post
export class PostEventActivityPayloadDto extends IntersectionType(EventActivityBaseDto, EventActivityStatusBaseDto) {}

export class PostEventActivityResponseDto extends IntersectionType(EventActivityBaseDto, EventActivityStatusBaseDto) {
  @ApiProperty({ description: 'data', required: false })
  @IsOptional()
  data?: any;
}

//#endregion DTO Post

//#region DTO Get list
export class GetListEventActivityParamDto {
  @ApiProperty({ description: 'Current page number' })
  @IsNumber()
  page: number;

  @ApiProperty({ description: 'Number of items per page' })
  @IsNumber()
  pageSize: number;

  @ApiProperty({ description: 'Phone number' })
  phoneNumber?: number;

  @ApiProperty({ description: 'Landing event' })
  @IsString()
  eventId: string;
}
export class GetListEventActivityResponseDto {
  @ApiProperty({
    type: ItemEventActivityDto,
    isArray: true,
    description: 'Danh sách item dữ liệu theo phân trang',
  })
  @ValidateNested({ each: true })
  @Type(() => ItemEventActivityDto)
  data: ItemEventActivityDto[];

  @ApiProperty({
    type: MetaDto,
    description: 'Thông tin phân trang',
  })
  @ValidateNested()
  @Type(() => MetaDto)
  meta: MetaDto;
}
//#endregion DTO Get list

//#region DTO Get by id
export class GetEventActivityResponseDto {
  @ApiProperty({
    type: ItemEventActivityDto,
    description: 'item dữ liệu Event Activity',
  })
  @ValidateNested()
  @Type(() => ItemEventActivityDto)
  data: ItemEventActivityDto;
}
//#endregion DTO Get by id

//#region get conductedSurvey
export class AnswerDto {
  @IsOptional()
  @IsNumber()
  optionId?: number;

  @IsOptional()
  @IsString()
  optionValue?: string;

  @IsOptional()
  @IsString()
  noteValue?: string;
}

export class SurveyResponseDto {
  @IsNumber()
  questionId: number;

  @IsString()
  questionValue: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AnswerDto)
  answers: AnswerDto[];
}

export class ConductedSurveyDataDto {
  @IsString()
  surveyId: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SurveyResponseDto)
  response: SurveyResponseDto[];
}

export class ConductedSurveyDto {
  @IsBoolean()
  activity_finished: boolean;

  @ValidateNested()
  @Type(() => ConductedSurveyDataDto)
  conducted_survey_data: ConductedSurveyDataDto;
}
//#endregion get conductedSurvey
