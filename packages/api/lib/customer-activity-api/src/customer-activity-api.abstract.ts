import { Injectable } from '@nestjs/common';
import { HttpClientRequestConfig } from '../../http-client/src';
import {
  ConductedSurveyDto,
  //GetEventActivityParamDto,
  GetEventActivityResponseDto,
  //GetEventParticipantParamDto,
  GetEventParticipantResponseDto,
  GetListEventActivityParamDto,
  GetListEventActivityResponseDto,
  GetListEventParticipantParamDto,
  GetListEventParticipantResponseDto,
  PostEventActivityPayloadDto,
  PostEventActivityResponseDto,
  PostEventParticipantPayloadDto,
  PostEventParticipantResponseDto,
  RemainingItemQuotaDto,
} from './dto';
@Injectable()
export abstract class CustomerActivityApiAbstract {
  //#region Event-activity
  abstract postEventActivity(
    payload: PostEventActivityPayloadDto,
    configs?: HttpClientRequestConfig,
  ): Promise<PostEventActivityResponseDto>;

  abstract getListEventActivity(
    param: GetListEventActivityParamDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetListEventActivityResponseDto>;

  abstract getEventActivity(id: string, configs?: HttpClientRequestConfig): Promise<GetEventActivityResponseDto>;

  abstract getConductedSurvey(
    event_id: string,
    user_phone_number: string,
    configs?: HttpClientRequestConfig,
  ): Promise<ConductedSurveyDto>;

  abstract putEventActivity(
    id: string,
    payload: PostEventActivityPayloadDto,
    configs?: HttpClientRequestConfig,
  ): Promise<PostEventActivityResponseDto>;

  abstract deleteEventActivity(id: string, configs?: HttpClientRequestConfig): Promise<boolean>;

  abstract getListEventActivityByPhoneAndEventId(
    param: { phone: string; eventId: string },
    configs?: HttpClientRequestConfig,
  ): Promise<GetListEventActivityResponseDto | null>;
  //#endregion Event-activity

  //#region Event-participant
  abstract postEventParticipant(
    payload: PostEventParticipantPayloadDto,
    configs?: HttpClientRequestConfig,
  ): Promise<PostEventParticipantResponseDto>;

  abstract getListEventParticipant(
    param: GetListEventParticipantParamDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetListEventParticipantResponseDto>;

  abstract getEventParticipant(id: string, configs?: HttpClientRequestConfig): Promise<GetEventParticipantResponseDto>;

  abstract putEventParticipant(
    id: string,
    payload: PostEventParticipantPayloadDto,
    configs?: HttpClientRequestConfig,
  ): Promise<PostEventParticipantResponseDto>;

  abstract deleteEventParticipant(id: string, configs?: HttpClientRequestConfig): Promise<boolean>;

  abstract getListEventParticipantByPhoneAndEventId(
    param: { phone: string; eventId: string },
    configs?: HttpClientRequestConfig,
  ): Promise<GetListEventParticipantResponseDto | null>;

  abstract getRemainingQuotaByPhone(
    param: { phoneNumber: string; eventId: string },
    configs?: HttpClientRequestConfig,
  ): Promise<{ remaining_event_ticket_quota: number } | null>;
  //#endregion Event-participant
}
