import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import { _checkExceptionFromAxiosRes } from '../../functions';
import { Injectable } from '@nestjs/common';
import {
  ConductedSurveyDto,
  //GetEventActivityParamDto,
  GetEventActivityResponseDto,
  //GetEventParticipantParamDto,
  GetEventParticipantResponseDto,
  GetListEventActivityParamDto,
  GetListEventActivityResponseDto,
  GetListEventParticipantParamDto,
  GetListEventParticipantResponseDto,
  PostEventActivityPayloadDto,
  PostEventActivityResponseDto,
  PostEventParticipantPayloadDto,
  PostEventParticipantResponseDto,
  RemainingItemQuotaDto,
} from './dto';

import { CustomerActivityApiAbstract } from './customer-activity-api.abstract';
import { CustomerActivityApiEndpoint } from './customer-activity-api.endpoint';
import { ActivityType } from './constants/app.constant';
@Injectable()
export class CustomerActivityApiService extends CustomerActivityApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  //#region Event-activity

  async getConductedSurvey(
    event_id: string,
    user_phone_number: string,
    configs?: HttpClientRequestConfig,
  ): Promise<ConductedSurveyDto> {
    const params = {
      event_id,
      user_phone_number,
    };

    const axiosRes = await this.httpClientService.get(CustomerActivityApiEndpoint.conductedSurvey, {
      params,
      ...configs,
    });

    _checkExceptionFromAxiosRes(axiosRes);

    return axiosRes?.data ?? null;
  }

  async postEventActivity(
    payload: PostEventActivityPayloadDto,
    configs?: HttpClientRequestConfig,
  ): Promise<PostEventActivityResponseDto> {
    const axiosRes = await this.httpClientService.post(
      CustomerActivityApiEndpoint.eventActivity,
      { data: payload },
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  async getListEventActivityByPhoneAndEventId(
    param: { phone: string; eventId: string },
    configs?: HttpClientRequestConfig,
  ): Promise<GetListEventActivityResponseDto | null> {
    const { phone, eventId } = param;

    const params = {
      'filters[user_phone_number][$eq]': phone,
      'filters[event_id][$eq]': eventId,
      'pagination[page]': 1,
      'pagination[pageSize]': 900,
      sort: 'createdAt:DESC',
    };

    const axiosRes = await this.httpClientService.get(CustomerActivityApiEndpoint.eventActivity, {
      params,
      ...configs,
    });

    _checkExceptionFromAxiosRes(axiosRes);

    return axiosRes?.data ?? null;
  }

  async getListEventActivity(
    param: GetListEventActivityParamDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetListEventActivityResponseDto | null> {
    const dataRequest = {
      'pagination[page]': param?.page,
      'pagination[pageSize]': param?.pageSize,
      'filters[user_phone_number][$contains]': param?.phoneNumber,
      'filters[event_id][$eq]': param?.eventId,
      'filters[received_item_status]': true,
      'filters[activity_type]': ActivityType.RECEIVE_ITEM,
      sort: 'createdAt:DESC',
    };
    const axiosRes = await this.httpClientService.get(CustomerActivityApiEndpoint.eventActivity, {
      params: { ...dataRequest },
      ...configs,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data ?? null;
  }

  async getEventActivity(
    //param: GetEventActivityParamDto,
    id: string,
    configs?: HttpClientRequestConfig,
  ): Promise<GetEventActivityResponseDto | null> {
    const url = `${CustomerActivityApiEndpoint.eventActivity}/${id}`;
    const axiosRes = await this.httpClientService.get(url, { ...configs });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data ?? null;
  }

  async putEventActivity(
    id: string,
    payload: PostEventActivityPayloadDto,
    configs?: HttpClientRequestConfig,
  ): Promise<PostEventActivityResponseDto> {
    const url = `${CustomerActivityApiEndpoint.eventActivity}/${id}`;
    const axiosRes = await this.httpClientService.put(url, { data: payload }, { ...configs });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  async deleteEventActivity(id: string, configs?: HttpClientRequestConfig): Promise<boolean> {
    const url = `${CustomerActivityApiEndpoint.eventActivity}/${id}`;
    const axiosRes = await this.httpClientService.delete(url, { ...configs });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data ?? null;
  }
  //#endregion Event-activity

  //#region Event-participant
  async postEventParticipant(
    payload: PostEventParticipantPayloadDto,
    configs?: HttpClientRequestConfig,
  ): Promise<PostEventParticipantResponseDto> {
    const axiosRes = await this.httpClientService.post(
      CustomerActivityApiEndpoint.eventParticipant,
      { data: payload },
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  async getListEventParticipant(
    param: GetListEventParticipantParamDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetListEventParticipantResponseDto> {
    const axiosRes = await this.httpClientService.get(CustomerActivityApiEndpoint.eventParticipant, {
      params: param?.pagination,
      ...configs,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data ?? null;
  }

  async getEventParticipant(
    //param: GetEventParticipantParamDto,
    id: string,
    configs?: HttpClientRequestConfig,
  ): Promise<GetEventParticipantResponseDto> {
    const url = `${CustomerActivityApiEndpoint.eventParticipant}/${id}`;
    const axiosRes = await this.httpClientService.get(url, { ...configs });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data ?? null;
  }

  async putEventParticipant(
    id: string,
    payload: PostEventParticipantPayloadDto,
    configs?: HttpClientRequestConfig,
  ): Promise<PostEventParticipantResponseDto> {
    const url = `${CustomerActivityApiEndpoint.eventParticipant}/${id}`;
    const axiosRes = await this.httpClientService.put(url, { data: payload }, { ...configs });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  async deleteEventParticipant(id: string, configs?: HttpClientRequestConfig): Promise<boolean> {
    const url = `${CustomerActivityApiEndpoint.eventParticipant}/${id}`;
    const axiosRes = await this.httpClientService.delete(url, { ...configs });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data ?? null;
  }

  async getListEventParticipantByPhoneAndEventId(
    param: { phone: string; eventId: string },
    configs?: HttpClientRequestConfig,
  ): Promise<GetListEventParticipantResponseDto | null> {
    const { phone, eventId } = param;

    const params = {
      'filters[phone_number][$eq]': phone,
      'filters[event_id][$eq]': eventId,
      'pagination[page]': 1,
      'pagination[pageSize]': 900,
    };

    const axiosRes = await this.httpClientService.get(CustomerActivityApiEndpoint.eventParticipant, {
      params,
      ...configs,
    });

    _checkExceptionFromAxiosRes(axiosRes);

    return axiosRes?.data ?? null;
  }
  async getRemainingQuotaByPhone(
    data: { phoneNumber: string; eventId: string },
    configs?: HttpClientRequestConfig,
  ): Promise<{ remaining_event_ticket_quota: number; name: string }> {
    const { phoneNumber, eventId } = data;

    const params = {
      user_phone_number: phoneNumber,
      event_id: eventId,
    };

    const axiosRes = await this.httpClientService.get(CustomerActivityApiEndpoint.getRemainingQuota, {
      ...configs,
      params,
    });

    _checkExceptionFromAxiosRes(axiosRes);

    return axiosRes?.data || null;
  }

  async getTicketByPhone(
    param: { phoneNumber: string; eventId: string },
    configs?: HttpClientRequestConfig,
  ): Promise<GetListEventActivityResponseDto | null> {
    const { phoneNumber, eventId } = param;

    const params = {
      'filters[user_phone_number][$eq]': phoneNumber,
      'filters[event_id][$eq]': eventId,
      'filters[received_item_status]': true,
      'filters[activity_type]': ActivityType.RECEIVE_ITEM,
      'pagination[page]': 1,
      'pagination[pageSize]': 100,
      sort: 'createdAt:DESC',
    };

    const axiosRes = await this.httpClientService.get(CustomerActivityApiEndpoint.eventActivity, {
      params,
      ...configs,
    });

    _checkExceptionFromAxiosRes(axiosRes);

    return axiosRes?.data ?? null;
  }
  //#endregion Event-participant
}
