import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

export class UpdateHeadersDto {
  @ApiProperty()
  @Expose()
  @IsString()
  email: string;
}
export class UpdateHeadersRes {
  @ApiProperty()
  @Expose()
  @IsOptional()
  isSuccess?: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  message?: string;
}
