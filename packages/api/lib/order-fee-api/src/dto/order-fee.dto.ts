import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';

export class CreateOrderExtraFeeDetailsDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  orderCode: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sku: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  skuName: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  orderDetailAttachmentCode: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  amount: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  metadata?: string; // Thông tin bổ sung
}

export class CreateOrderExtraFeeDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  transactionCode: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  transactionType: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  shopCode: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  shopName: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalAmount: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createdBy: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  paymentCode: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  lcvId: string; // Người tiêm

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  InjectedPersonName: string; // Người tiêm

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  customerName: string; //Người mua

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  customerPhone: string; //Người mua

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  nationalVaccineCode: string; //Người mua

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  custCode: string; //Người mua

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  orderReferenceID: string; // Mã đơn hàng tham chiếu

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  orderChannel: number;

  @ApiProperty({ type: [CreateOrderExtraFeeDetailsDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateOrderExtraFeeDetailsDto)
  createOrderExtraFeeDetails: CreateOrderExtraFeeDetailsDto[];
}

// Response

export class OrderExtraFeeDetailDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  orderExtraFeeId: string;

  @ApiProperty()
  @Expose()
  sku: string;

  @ApiProperty()
  @Expose()
  skuName: string;

  @ApiProperty()
  @Expose()
  orderDetailAttachmentCode: string;

  @ApiProperty()
  @Expose()
  amount: number;

  @ApiProperty()
  @Expose()
  orderCode: string;

  @ApiProperty()
  @Expose()
  metadata: string; // Mã đơn hàng tham chiếu
}

export class GetOrderFeeByOrderCodesResponseDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  transactionType: number;

  @ApiProperty()
  @Expose()
  transactionCode: string;

  @ApiProperty()
  @Expose()
  paymentCode: string;

  @ApiProperty()
  @Expose()
  shopCode: string;

  @ApiProperty()
  @Expose()
  shopName: string;

  @ApiProperty()
  @Expose()
  totalAmount: number;

  @ApiProperty()
  @Expose()
  createdBy: string;

  @ApiProperty()
  @Expose()
  createdDate: string;

  @ApiProperty()
  @Expose()
  modifiedBy: string;

  @ApiProperty()
  @Expose()
  modifiedDate: string;

  @ApiProperty()
  @Expose()
  lcvId: string; // Người tiêm

  @ApiProperty()
  @Expose()
  InjectedPersonName: string; // Người tiêm

  @ApiProperty()
  @Expose()
  customerName: string; //Người mua

  @ApiProperty()
  @Expose()
  customerPhone: string; //Người mua

  @ApiProperty()
  @Expose()
  nationalVaccineCode: string; //Người mua

  @ApiProperty()
  @Expose()
  custCode: string; //Người mua

  @ApiProperty()
  @Expose()
  orderReferenceID: string; // Mã đơn hàng tham chiếu

  @ApiProperty()
  @Expose()
  status: number;

  @ApiProperty()
  @Expose()
  orderChannel: number;

  @ApiProperty()
  @Expose()
  orderExtraFeeDetails: OrderExtraFeeDetailDto[];
}

export class UpdateOrderExtraFeeByTransactionCodeDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  transactionCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  transactionType?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  paymentCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  shopCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  shopName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalAmount?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  modifiedBy?: string;
}

export class GetAllOrderExtraFeeDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  maxResultCount?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  skipCount?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({ required: false, type: [Number] })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  transactionType?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  shopCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  fromTime?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  toTime?: string;

  @ApiProperty({ required: false, type: Number, isArray: true })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  status?: number[];

  @ApiProperty({ required: false, type: Number, isArray: true })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  orderChannel?: number[];
}

export class OrderExtraFeeDetailWithOrderCodeDto extends OrderExtraFeeDetailDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  orderExtraFeeId: string;

  @ApiProperty()
  @Expose()
  orderCode: string;
}

export class GetAllOrderExtraFeeResponseDto {
  @ApiProperty()
  @Expose()
  totalCount: number;

  @ApiProperty({ type: [GetOrderFeeByOrderCodesResponseDto] })
  @Expose()
  @Type(() => GetOrderFeeByOrderCodesResponseDto)
  items: GetOrderFeeByOrderCodesResponseDto[];
}

export class ChangeOrderExtraFeeStatusDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  modifiedBy: string;

  @ApiProperty()
  @IsNumber()
  status: number;
}

export class GetOrderFeeByLcvIdDto {
  @ApiProperty({ type: [String], description: 'List of LCV IDs' })
  @IsArray()
  @IsString({ each: true })
  lcvIds: string[];
}
