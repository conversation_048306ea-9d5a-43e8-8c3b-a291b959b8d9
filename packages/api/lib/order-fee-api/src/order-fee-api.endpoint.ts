export enum OrderFeeApiEndpoint {
  GET_ORDER_FEE_BY_ORDER_CODE = 'api/order-extra-fee/get-by-order-code',
  CREATE_ORDER_EXTRA_FEE = 'api/order-extra-fee',
  UPDATE_ORDER_EXTRA_FEE_BY_TRANSACTION_CODE = 'api/order-extra-fee/update-by-transaction-code',
  GET_ORDER_FEE_BY_TRANSACTION_CODE = 'api/order-extra-fee/get-by-transaction-code',
  GET_ORDER_FEE_BY_ORDER_REFERENCE_ID = 'api/order-extra-fee/get-by-order-reference-id',
  GET_ORDER_FEE_BY_LCV_ID = 'api/order-extra-fee/get-by-lcv-id',
  GET_ALL_ORDER_EXTRA_FEE = 'api/order-extra-fee/get-all',
  CHANGE_ORDER_EXTRA_FEE_STATUS = 'api/order-extra-fee/change-status',
}
