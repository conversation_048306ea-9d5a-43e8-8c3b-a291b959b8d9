import { Injectable } from '@nestjs/common';
import { _checkExceptionFromAxiosRes } from '../../functions';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import {
  CreateOrderExtraFeeDto,
  GetOrderFeeByOrderCodesResponseDto,
  UpdateOrderExtraFeeByTransactionCodeDto,
  GetAllOrderExtraFeeDto,
  GetAllOrderExtraFeeResponseDto,
  ChangeOrderExtraFeeStatusDto,
} from './dto';
import { OrderFeeApiEndpoint } from './order-fee-api.endpoint';
import { OrderFeeApiAbstract } from './order-fee-api.abstract';

@Injectable()
export class OrderFeeApiService extends OrderFeeApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }
  async getOrderFeeByOrderCodes(
    orderCodes: string[],
    configs?: HttpClientRequestConfig,
  ): Promise<GetOrderFeeByOrderCodesResponseDto[]> {
    const axiosRes = await this.httpClientService.post(
      OrderFeeApiEndpoint.GET_ORDER_FEE_BY_ORDER_CODE,
      {
        orderCodes,
      },
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  async createOrderExtraFee(
    body: CreateOrderExtraFeeDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetOrderFeeByOrderCodesResponseDto> {
    const axiosRes = await this.httpClientService.post(OrderFeeApiEndpoint.CREATE_ORDER_EXTRA_FEE, body, configs);
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  async updateOrderExtraFeeByTransactionCode(
    body: UpdateOrderExtraFeeByTransactionCodeDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetOrderFeeByOrderCodesResponseDto> {
    const axiosRes = await this.httpClientService.put(
      OrderFeeApiEndpoint.UPDATE_ORDER_EXTRA_FEE_BY_TRANSACTION_CODE,
      body,
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  async getOrderFeeByTransactionCodes(
    transactionCodes: string[],
    configs?: HttpClientRequestConfig,
  ): Promise<GetOrderFeeByOrderCodesResponseDto[]> {
    const axiosRes = await this.httpClientService.post(
      OrderFeeApiEndpoint.GET_ORDER_FEE_BY_TRANSACTION_CODE,
      {
        transactionCodes,
      },
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  async getOrderFeeByOrderReferenceIds(
    orderReferenceIds: string[],
    configs?: HttpClientRequestConfig,
  ): Promise<GetOrderFeeByOrderCodesResponseDto[]> {
    const axiosRes = await this.httpClientService.post(
      OrderFeeApiEndpoint.GET_ORDER_FEE_BY_ORDER_REFERENCE_ID,
      {
        orderReferenceIds,
      },
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  async getAllOrderExtraFee(
    body: GetAllOrderExtraFeeDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetAllOrderExtraFeeResponseDto> {
    const axiosRes = await this.httpClientService.post(OrderFeeApiEndpoint.GET_ALL_ORDER_EXTRA_FEE, body, configs);
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  async changeOrderExtraFeeStatus(
    body: ChangeOrderExtraFeeStatusDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetOrderFeeByOrderCodesResponseDto> {
    const axiosRes = await this.httpClientService.put(OrderFeeApiEndpoint.CHANGE_ORDER_EXTRA_FEE_STATUS, body, configs);
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  async getOrderFeeByLcvIds(
    lcvIds: string[],
    configs?: HttpClientRequestConfig,
  ): Promise<GetOrderFeeByOrderCodesResponseDto[]> {
    const axiosRes = await this.httpClientService.post(
      OrderFeeApiEndpoint.GET_ORDER_FEE_BY_LCV_ID,
      {
        lcvIds,
      },
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }
}
