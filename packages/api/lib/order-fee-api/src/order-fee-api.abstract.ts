import { HttpClientRequestConfig } from '../../http-client/src';
import {
  CreateOrderExtraFeeDto,
  GetOrderFeeByOrderCodesResponseDto,
  UpdateOrderExtraFeeByTransactionCodeDto,
  GetAllOrderExtraFeeDto,
  GetAllOrderExtraFeeResponseDto,
  ChangeOrderExtraFeeStatusDto,
} from './dto';

export abstract class OrderFeeApiAbstract {
  abstract getOrderFeeByOrderCodes(
    orderCodes: string[],
    configs?: HttpClientRequestConfig,
  ): Promise<GetOrderFeeByOrderCodesResponseDto[]>;

  abstract createOrderExtraFee(
    body: CreateOrderExtraFeeDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetOrderFeeByOrderCodesResponseDto>;

  abstract updateOrderExtraFeeByTransactionCode(
    body: UpdateOrderExtraFeeByTransactionCodeDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetOrderFeeByOrderCodesResponseDto>;

  abstract getOrderFeeByTransactionCodes(
    transactionCodes: string[],
    configs?: HttpClientRequestConfig,
  ): Promise<GetOrderFeeByOrderCodesResponseDto[]>;

  abstract getOrderFeeByOrderReferenceIds(
    orderReferenceIds: string[],
    configs?: HttpClientRequestConfig,
  ): Promise<GetOrderFeeByOrderCodesResponseDto[]>;

  abstract getAllOrderExtraFee(
    body: GetAllOrderExtraFeeDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetAllOrderExtraFeeResponseDto>;

  abstract changeOrderExtraFeeStatus(
    body: ChangeOrderExtraFeeStatusDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetOrderFeeByOrderCodesResponseDto>;

  abstract getOrderFeeByLcvIds(
    lcvIds: string[],
    configs?: HttpClientRequestConfig,
  ): Promise<GetOrderFeeByOrderCodesResponseDto[]>;
}
