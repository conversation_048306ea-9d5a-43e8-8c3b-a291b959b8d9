import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import { _checkExceptionFromAxiosRes } from '../../functions';
import { Injectable } from '@nestjs/common';

import { OpenStreetMapApiAbstract } from './open-street-map-api.abstract';
import { OpenStreetMapApiEndpoint } from './open-street-map-api.endpoint';
import { GetDirectionPayloadDto, GetDirectionResponseDto } from './dto';
import { GetTablePayloadDto, GetTableResponseDto } from './dto/open-street-map.dto';

@Injectable()
export class OpenStreetMapApiService extends OpenStreetMapApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  async getDirection(
    param: GetDirectionPayloadDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetDirectionResponseDto | null> {
    const { customerLat, customerLng, shopLat, shopLng } = param;
    const url = `${OpenStreetMapApiEndpoint.OPEN_STREET_MAP_DIRECTIONS_API}${customerLng},${customerLat};${shopLng},${shopLat}?overview=false`;

    const axiosRes = await this.httpClientService.get(url, { ...configs });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data ?? null;
  }

  async getTable(param: GetTablePayloadDto, configs?: HttpClientRequestConfig): Promise<GetTableResponseDto | null> {
    const { coordinates, sources, annotations } = param;
    const url = `${OpenStreetMapApiEndpoint.OPEN_STREET_MAP_TABLE_API}${coordinates}?sources=${sources}&annotations=${annotations}`;

    const axiosRes = await this.httpClientService.get(url, { ...configs });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data ?? null;
  }
}
