import { Injectable } from '@nestjs/common';
import { GetDirectionPayloadDto, GetDirectionResponseDto } from './dto';
import { HttpClientRequestConfig } from '../../http-client/src';
import { GetTablePayloadDto, GetTableResponseDto } from './dto/open-street-map.dto';

@Injectable()
export abstract class OpenStreetMapApiAbstract {
  //#region Open street map
  abstract getDirection(
    payload: GetDirectionPayloadDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetDirectionResponseDto>;
  abstract getTable(payload: GetTablePayloadDto, configs?: HttpClientRequestConfig): Promise<GetTableResponseDto>;
}
