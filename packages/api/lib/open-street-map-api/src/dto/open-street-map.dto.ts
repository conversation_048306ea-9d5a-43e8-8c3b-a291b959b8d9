import { ApiProperty } from '@nestjs/swagger';

export class GetDirectionPayloadDto {
  @ApiProperty({
    description: 'Customer latitude',
    example: 10.837855025837346,
  })
  customerLat: number;

  @ApiProperty({
    description: 'Customer longitude',
    example: 106.8328416448703,
  })
  customerLng: number;

  @ApiProperty({
    description: 'Shop latitude',
    example: 10.837855025837346,
  })
  shopLat: number;

  @ApiProperty({
    description: 'Shop longitude',
    example: 105.8328416448703,
  })
  shopLng: number;
}

export class RouteLegsDto {
  @ApiProperty({
    description: 'Steps of the route',
    type: [Object],
  })
  steps: any[];

  @ApiProperty()
  weight: number;

  @ApiProperty()
  duration: number;

  @ApiProperty()
  distance: number;

  @ApiProperty()
  summary: string;
}

export class RouteDto {
  @ApiProperty({
    type: [RouteLegsDto],
  })
  legs: RouteLegsDto[];

  @ApiProperty()
  weight_name: string;

  @ApiProperty()
  weight: number;

  @ApiProperty()
  duration: number;

  @ApiProperty()
  distance: number;
}

export class WaypointDto {
  @ApiProperty()
  hint: string;

  @ApiProperty({
    type: [Number],
    example: [106.83294, 10.837589],
  })
  location: number[];

  @ApiProperty()
  name: string;

  @ApiProperty()
  distance: number;
}

export class GetDirectionResponseDto {
  @ApiProperty()
  code: string;

  @ApiProperty({
    type: [RouteDto],
  })
  routes: RouteDto[];

  @ApiProperty({
    type: [WaypointDto],
  })
  waypoints: WaypointDto[];
}

export class GetTablePayloadDto {
  @ApiProperty({
    description: 'List of coordinates',
    example: '106.8328,10.8378;106.7004,10.7757',
  })
  coordinates: string;

  @ApiProperty({
    description: 'Source index',
    example: 0,
  })
  sources: number;

  @ApiProperty({
    description: 'Annotations',
    example: 'distance',
  })
  annotations: string;
}

export class TableDestinationDto {
  @ApiProperty()
  hint: string;

  @ApiProperty({ type: [Number] })
  location: number[];

  @ApiProperty()
  name: string;

  @ApiProperty()
  distance: number;
}

export class GetTableResponseDto {
  @ApiProperty()
  code: string;

  @ApiProperty({ type: [[Number]] })
  distances: number[][];

  @ApiProperty({ type: [TableDestinationDto] })
  destinations: TableDestinationDto[];

  @ApiProperty({ type: [TableDestinationDto] })
  sources: TableDestinationDto[];
}
