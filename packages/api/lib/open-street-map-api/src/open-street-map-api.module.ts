import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MAX_REDIRECT, TIMEOUT } from '../../constants';
import { HttpClientModule } from '../../http-client/src';
import { OpenStreetMapApiService } from './open-street-map-api.service';

@Module({
  imports: [
    HttpClientModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        baseURL: configService.get('OPEN_STREET_MAP_API_URL'),
        timeout: TIMEOUT,
        maxRedirects: MAX_REDIRECT,
        validateStatus: () => {
          return true;
        },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [OpenStreetMapApiService],
  exports: [OpenStreetMapApiService],
})
export class OpenStreetMapApiModule {}
