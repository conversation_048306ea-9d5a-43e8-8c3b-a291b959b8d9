import { Injectable } from '@nestjs/common';
import { HttpClientRequestConfig } from '../../http-client/src';
import { SendBillPostponeZaloDto, SendBillZaloDto } from './dto';

@Injectable()
export abstract class CamundaNotificationApiAbstract {
  /**
   * @description Gửi thông báo hóa đơn qua Zalo
   * @param payload Thông tin hóa đơn gửi Zalo
   * @param configs Cấu hình HTTP client (tùy chọn)
   * @returns Trả về true nếu gửi thành công, ngược lại là false
   */
  abstract sendNotificationBillZalo(payload: SendBillZaloDto, configs?: HttpClientRequestConfig): Promise<boolean>;
  /**
   * @description Gửi thông báo hóa đơn trễ hẹn qua Zalo
   * @param payload Thông tin hóa đơn gửi Zalo
   * @param configs Cấu hình HTTP client (tùy chọn)
   * @returns Trả về true nếu gửi thành công, ngược lại là false
   */
  abstract sendNotificationBillPostPoneZalo(
    payload: SendBillPostponeZaloDto,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean>;
}
