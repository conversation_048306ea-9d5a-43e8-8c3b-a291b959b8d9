import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';

export class SendBillZaloDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  orderCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  url?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  isManualSendBill?: boolean;
}
