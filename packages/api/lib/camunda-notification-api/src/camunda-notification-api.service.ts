import { HttpStatus, Injectable } from '@nestjs/common';
import { CoreException } from '../../exceptions/core.exception';
import { HttpClientRequestConfig, HttpClientResponse, HttpClientService } from '../../http-client/src';
import { CamundaNotificationApiAbstract } from './camunda-notification-api.abstract';
import { CamundaNotificationApiEndpoint } from './camunda-notification-api.endpoint';
import { SendBillPostponeZaloDto, SendBillZaloDto } from './dto';

@Injectable()
export class CamundaNotificationApiService extends CamundaNotificationApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  protected _checkExceptionFromAxiosRes(axiosRes: HttpClientResponse) {
    if (!axiosRes) return null;
    if (![HttpStatus.OK, HttpStatus.CREATED].includes(axiosRes.status)) {
      throw new CoreException(axiosRes.data, axiosRes.status, axiosRes.config);
    }
  }

  /**
   * @description <PERSON><PERSON><PERSON> thông báo hóa đơn qua Zalo
   * @param payload Thông tin hóa đơn gửi Zalo
   * @param configs Cấu hình HTTP client (tùy chọn)
   * @returns Trả về true nếu gửi thành công, ngược lại là false
   */
  async sendNotificationBillZalo(payload: SendBillZaloDto, configs?: HttpClientRequestConfig): Promise<boolean> {
    const axiosRes = await this.httpClientService.post(
      CamundaNotificationApiEndpoint.SEND_NOTIFICATION_BILL_ZALO,
      payload,
      configs,
    );
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data === true;
  }

  /**
   * @description Gửi thông báo hóa đơn qua Zalo
   * @param payload Thông tin hóa đơn gửi Zalo
   * @param configs Cấu hình HTTP client (tùy chọn)
   * @returns Trả về true nếu gửi thành công, ngược lại là false
   */
  async sendNotificationBillPostPoneZalo(
    payload: SendBillPostponeZaloDto,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean> {
    const axiosRes = await this.httpClientService.post(
      CamundaNotificationApiEndpoint.SEND_NOTIFICATION_BILL_POSTPONE_ZALO,
      payload,
      configs,
    );
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data === true;
  }
}
