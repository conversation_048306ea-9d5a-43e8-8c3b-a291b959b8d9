import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MAX_REDIRECT, TIMEOUT } from '../../constants';
import { HttpClientModule } from '../../http-client/src';
import { CamundaNotificationApiService } from './camunda-notification-api.service';

@Module({
  imports: [
    HttpClientModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        baseURL: configService.get('CAMUNDA_NOTIFICATION_API_URL'),
        timeout: TIMEOUT,
        maxRedirects: MAX_REDIRECT,
        validateStatus: () => true,
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [CamundaNotificationApiService],
  exports: [CamundaNotificationApiService],
})
export class CamundaNotificationApiModule {}
