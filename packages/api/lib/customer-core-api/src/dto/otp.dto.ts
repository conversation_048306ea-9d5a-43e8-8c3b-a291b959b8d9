import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';

export class CreateOtpReqLibDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  phoneNumber: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  fromSystem: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  major: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  templateId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  sender: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  expired: string;
}

export class ValidateOtpReqLibDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  PhoneNumber: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  Otp: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  FromSystem: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  Major: string;
}

export class CreateEmailOtpReqLibDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  email: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  fromSystem: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  major: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  templateId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  sender: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  expired: string;
}

export class ValidateEmailOtpReqLibDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  email: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  otp: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  fromSystem: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  major: string;
}

export class OtpResLibDto {
  @ApiProperty()
  @Expose()
  message: string;

  @ApiProperty()
  @Expose()
  isValid: boolean;
}
