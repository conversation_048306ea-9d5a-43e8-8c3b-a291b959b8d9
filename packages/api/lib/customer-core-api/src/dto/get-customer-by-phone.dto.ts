import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsArray, IsOptional, IsString } from 'class-validator';

export class GetCustomerByPhoneDto {
  @ApiProperty({ description: 'Mobile phone number', example: '0909123456' })
  @IsString()
  @Expose()
  mobilePhone: string;

  @ApiProperty({
    description: 'Attributes to include',
    example: ['PhoneNumber'],
    isArray: true,
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @Expose()
  includeAttributes?: string[];
}
