import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, ValidateNested } from 'class-validator';
import { Expose, Type } from 'class-transformer';

export class CustomerProfileDTO {
  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  mobilePhone?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  firstName?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  middleName?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  lastName?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  fullName?: string;

  @ApiProperty({ type: Number })
  @Expose()
  @IsOptional()
  gender?: number;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  cardId?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  dateIdentifier?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  birthDate?: string;

  @ApiProperty({ type: Number })
  @Expose()
  @IsOptional()
  customerType?: number;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  taxNumber?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  email?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  image?: string;
}

export class CustomerWorkDTO {
  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  jobTitle?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  position?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  workCompany?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  workPhone?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  workAddress?: string;
}

export class CustomerAddressDTO {
  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  mobilePhone?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  address?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  name?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  addressType?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  wardCode?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  districtCode?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  provinceCode?: string;

  @ApiProperty({ type: Boolean })
  @Expose()
  @IsOptional()
  isPrimary?: boolean;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  note?: string;

  @ApiProperty({ type: Boolean })
  @Expose()
  @IsOptional()
  isValid?: boolean;
}

export class CustomerIncludeAttributesDTO {
  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  additionalProp1?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  additionalProp2?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  additionalProp3?: string;
}

export class CustomerCreatedDTO {
  @ApiProperty({ type: CustomerProfileDTO })
  @Expose()
  @IsOptional()
  @ValidateNested()
  @Type(() => CustomerProfileDTO)
  profile?: CustomerProfileDTO;

  @ApiProperty({ type: CustomerWorkDTO })
  @Expose()
  @IsOptional()
  @ValidateNested()
  @Type(() => CustomerWorkDTO)
  work?: CustomerWorkDTO;

  @ApiProperty({ type: [CustomerAddressDTO] })
  @Expose()
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomerAddressDTO)
  address?: CustomerAddressDTO[];

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  fromSystem?: string;

  @ApiProperty({ type: CustomerIncludeAttributesDTO })
  @Expose()
  @IsOptional()
  @ValidateNested()
  @Type(() => CustomerIncludeAttributesDTO)
  includeAttributes?: CustomerIncludeAttributesDTO;
}
