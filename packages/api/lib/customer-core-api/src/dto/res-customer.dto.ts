import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, ValidateNested } from 'class-validator';
import { Expose, Type } from 'class-transformer';

export class CustomerProfileResponseDTO {
  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  firstName?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  middleName?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  lastName?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  fullName?: string;

  @ApiProperty({ type: Number })
  @Expose()
  @IsOptional()
  gender?: number;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  genderName?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  cardId?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  dateIdentifier?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  birthDate?: string;

  @ApiProperty({ type: Number })
  @Expose()
  @IsOptional()
  customerType?: number;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  customerTypeName?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  taxNumber?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  mobilePhone?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  email?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  image?: string;
}

export class CustomerWorkResponseDTO {
  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  jobTitle?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  position?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  workCompany?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  workPhone?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  workAddress?: string;
}

export class CustomerAddressResponseDTO {
  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  name?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  mobilePhone?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  addressType?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  address?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  wardCode?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  wardName?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  districtCode?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  districtName?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  provinceCode?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  provinceName?: string;

  @ApiProperty({ type: Boolean })
  @Expose()
  @IsOptional()
  isPrimary?: boolean;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  note?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  customerAddressId?: string;

  @ApiProperty({ type: Boolean })
  @Expose()
  @IsOptional()
  isValid?: boolean;

  @ApiProperty({ type: Boolean })
  @Expose()
  @IsOptional()
  isNew?: boolean;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  parentId?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  creationTime?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  lastModificationTime?: string;
}

export class CustomerIncludeAttributesResponseDTO {
  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  additionalProp1?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  additionalProp2?: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  additionalProp3?: string;
}

export class CustomerResponseDTO {
  @ApiProperty({ type: String })
  @Expose()
  @IsOptional()
  customerId?: string;

  @ApiProperty({ type: CustomerProfileResponseDTO })
  @Expose()
  @IsOptional()
  @ValidateNested()
  @Type(() => CustomerProfileResponseDTO)
  profile?: CustomerProfileResponseDTO;

  @ApiProperty({ type: CustomerWorkResponseDTO })
  @Expose()
  @IsOptional()
  @ValidateNested()
  @Type(() => CustomerWorkResponseDTO)
  work?: CustomerWorkResponseDTO;

  @ApiProperty({ type: [CustomerAddressResponseDTO] })
  @Expose()
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomerAddressResponseDTO)
  address?: CustomerAddressResponseDTO[];

  @ApiProperty({ type: CustomerIncludeAttributesResponseDTO })
  @Expose()
  @IsOptional()
  @ValidateNested()
  @Type(() => CustomerIncludeAttributesResponseDTO)
  includeAttributes?: CustomerIncludeAttributesResponseDTO;
}
