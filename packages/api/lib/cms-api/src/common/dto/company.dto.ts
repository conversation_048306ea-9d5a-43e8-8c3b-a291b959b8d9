// dto/company.dto.ts

import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ICompany } from '../interfaces/company.interface';
import { Image } from './common.dto';

export class CompanyResponseDto implements ICompany {
  @ApiProperty({ description: 'Tên doanh nghiệp' })
  @Expose()
  name: string;

  @ApiProperty({ description: 'Email doanh nghiệp' })
  @Expose()
  email: string;

  @ApiProperty({ type: Image, description: 'Logo đại diện doanh nghiệp' })
  @Expose()
  @Type(() => Image)
  logo: Image;
}
