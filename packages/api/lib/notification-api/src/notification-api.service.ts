import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import { _checkExceptionFromAxiosRes } from '../../functions';
import { Injectable } from '@nestjs/common';

import { NotificationApiAbstract } from './notification-api.abstract';
import { NotificationApiEndpoint } from './notification-api.endpoint';
import { SendNotificationPayloadDto, SendNotificationResponseDto } from './dto';
@Injectable()
export class NotificationApiService extends NotificationApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  //#region Notification
  async sendNotification(
    payload: SendNotificationPayloadDto,
    configs?: HttpClientRequestConfig,
  ): Promise<SendNotificationResponseDto> {
    const axiosRes = await this.httpClientService.post(NotificationApiEndpoint.notification, payload, {
      ...configs,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }
  //#endregion Notification
}
