import { ApiProperty } from '@nestjs/swagger';
import { IsInt, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class PaginationBaseDto {
  @ApiProperty({ example: 1, description: 'Current page number' })
  @IsInt()
  page: number;

  @ApiProperty({ example: 10, description: 'Number of items per page' })
  @IsInt()
  pageSize: number;
}

export class PaginationMetaDto extends PaginationBaseDto {
  @ApiProperty({ example: 5, description: 'Total number of pages' })
  @IsInt()
  pageCount: number;

  @ApiProperty({ example: 50, description: 'Total number of records' })
  @IsInt()
  total: number;
}

export class MetaDto {
  @ApiProperty({ type: PaginationMetaDto })
  @ValidateNested()
  @Type(() => PaginationMetaDto)
  pagination: PaginationMetaDto;
}
