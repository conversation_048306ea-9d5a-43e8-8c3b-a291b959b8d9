import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsArray, ValidateNested, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class ParamDto {
  @ApiProperty({ type: Object, example: {} })
  Title: Record<string, any>;

  @ApiProperty({ type: Object, example: { customerName: 'A Nam', code: '12345' } })
  Content: Record<string, any>;

  @ApiProperty({ type: Object, example: {} })
  ContentFailOver: Record<string, any>;

  @ApiProperty({ type: Object, example: {} })
  ExtraProperties: Record<string, any>;
}

export class MessageDto {
  @ApiProperty({ example: '47fd3c8d-6fb5-b0ef-fe89-3a1b1f609d14' })
  @IsString()
  TemplateId: string;

  @ApiProperty({ type: [String], example: ['0963101020'] })
  @IsArray()
  To: string[];

  @ApiProperty({ type: [String], example: [''] })
  @IsArray()
  Bcc: string[];

  @ApiProperty({ type: [String], example: [''] })
  @IsArray()
  Cc: string[];

  @ApiProperty({ type: () => ParamDto })
  @ValidateNested()
  @Type(() => ParamDto)
  Param: ParamDto;

  @ApiPropertyOptional({ example: '[ImageLink] (optional)' })
  @IsOptional()
  @IsString()
  ImageLink?: string;

  @ApiPropertyOptional({ example: '[MessageLink] (optional)' })
  @IsOptional()
  @IsString()
  MessageLink?: string;
}

export class SendNotificationPayloadDto {
  @ApiProperty({ example: 'Test' })
  @IsString()
  FromSys: string;

  @ApiProperty({ example: '22312' })
  @IsString()
  Sender: string;

  @ApiProperty({ type: [MessageDto] })
  @ValidateNested({ each: true })
  @Type(() => MessageDto)
  Messages: MessageDto[];
}

export class SendNotificationResponseDto {
  @ApiProperty({ example: 'f66a236a-d9e7-488d-9421-c9f921b4f4d5' })
  @IsString()
  transactionRequestId: string;

  @ApiProperty({ example: '' })
  @IsString()
  hangFireId: string;
}
