import { Injectable } from '@nestjs/common';
import { HttpClientRequestConfig } from '../../http-client/src';
import { SendNotificationPayloadDto, SendNotificationResponseDto } from './dto';
@Injectable()
export abstract class NotificationApiAbstract {
  //#region Notification
  abstract sendNotification(
    payload: SendNotificationPayloadDto,
    configs?: HttpClientRequestConfig,
  ): Promise<SendNotificationResponseDto>;

  //#endregion Notification
}
