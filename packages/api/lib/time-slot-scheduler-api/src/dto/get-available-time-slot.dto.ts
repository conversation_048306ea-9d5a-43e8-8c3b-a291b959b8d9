import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';

export class GetAvailableTimeSlotDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  lcvId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  shopCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  appointmentDate?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  suggestNextAvailable?: boolean;
}

export class TimeSlotQuotaDto {
  @ApiProperty()
  @IsOptional()
  @Expose()
  appointmentDate: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  timeSlot: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  timeSlotDescription: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  maxQuota: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  usedQuota: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  remainingQuota: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  isDefaulSelect: boolean;

  @ApiProperty()
  @IsOptional()
  @Expose()
  isAllowSelect: boolean;
}

export class GetAvailableTimeSlotResponseDto {
  @ApiProperty()
  @IsOptional()
  @Expose()
  lcvId: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  shopCode: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  timeSlotQuota: TimeSlotQuotaDto[];

  @ApiProperty()
  @IsOptional()
  @Expose()
  suggestNextDateAvailable: string[];
}
