import { Injectable } from '@nestjs/common';
import { _checkExceptionFromAxiosRes } from '../../functions';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import { BffCustomerApiEndpoint } from './bff-customer-api.endpoint';
import { BffCustomerApiAbstract } from './bff-customer-api.abstract';

@Injectable()
export class BffCustomerApiService extends BffCustomerApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }
  async clearCacheByPhone(phone: string, configs?: HttpClientRequestConfig) {
    const axiosRes = await this.httpClientService.post(
      BffCustomerApiEndpoint.CLEAR_CACHE_CUSTOMER_PROFILE_BY_PHONE,
      {
        phone,
      },
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data?.data || null;
  }
}
