export enum AffiliateApiEndpoint {
  // AffiliateOrganization endpoints
  AFFILIATE_ORGANIZATION_CREATE = 'api/affiliate-organizations/create',
  AFFILIATE_ORGANIZATION_UPDATE = 'api/affiliate-organizations/update',
  AFFILIATE_ORGANIZATION_UPDATE_STATUS = 'api/affiliate-organizations/update-status',
  AFFILIATE_ORGANIZATION_ACCEPT = 'api/affiliate-organizations/accept',
  AFFILIATE_ORGANIZATION_ES_SEARCH = 'api/affiliate-organizations/es/search',
  AFFILIATE_ORGANIZATION_ES_GET_BY_ID = 'api/affiliate-organizations/es/get/{0}',
  AFFILIATE_ORGANIZATION_ES_GET_BY_CODE = 'api/affiliate-organizations/es/get/{0}',

  // AffiliateUser endpoints
  AFFILIATE_USER_CREATE = 'api/affiliate-users/create',
  AFFILIATE_USER_UPDATE = 'api/affiliate-users/update',
  AFFILIATE_USER_UPDATE_STATUS = 'api/affiliate-users/update-status',
  AFFILIATE_USER_ACCEPT = 'api/affiliate-users/accept',
  AFFILIATE_USER_ES_SEARCH = 'api/affiliate-users/es/search',
  AFFILIATE_USER_ES_GET_BY_ID = 'api/affiliate-users/es/get/{0}',
  AFFILIATE_USER_ES_GET_BY_USER_CODE = 'api/affiliate-users/es/get/{0}',
  AFFILIATE_USER_ENUMS = 'api/affiliate-users/enums',
}
