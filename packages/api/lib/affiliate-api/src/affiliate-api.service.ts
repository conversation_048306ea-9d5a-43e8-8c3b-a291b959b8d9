import { HttpStatus, Injectable } from '@nestjs/common';
import { ClassConstructor, plainToInstance } from 'class-transformer';
import { CoreException } from '../../exceptions/core.exception';
import { HttpClientRequestConfig, HttpClientResponse, HttpClientService } from '../../http-client/src';
import { IErrorCoreResponse } from '../../interfaces';
import { AffiliateApiAbstract } from './affiliate-api.abstract';
import { AffiliateApiEndpoint } from './affiliate-api.endpoint';
import {
  AffiliateOrganizationCreateRequest,
  AffiliateOrganizationUpdateRequest,
  AffiliateOrganizationUpdateStatusRequest,
  AffiliateOrganizationAcceptRequest,
  AffiliateOrganizationSearchRequest,
  AffiliateOrganizationDto,
  AffiliateOrganizationDtoEs,
  NewAffiliateOrganizationResponse,
} from './dto/affiliate-organization.dto';
import {
  AffiliateUserCreateRequest,
  AffiliateUserUpdateRequest,
  AffiliateUserUpdateStatusRequest,
  AffiliateUserAcceptRequest,
  AffiliateUserSearchRequest,
  AffiliateUserDto,
} from './dto/affiliate-user.dto';
import { PagedResultDto } from './dto/common.dto';

@Injectable()
export class AffiliateApiService extends AffiliateApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  protected _checkExceptionFromAxiosRes(axiosRes: HttpClientResponse) {
    if (!axiosRes) return null;
    if (![HttpStatus.OK, HttpStatus.NO_CONTENT]?.includes(axiosRes.status)) {
      throw new CoreException(axiosRes.data as unknown as IErrorCoreResponse, axiosRes.status, axiosRes.config);
    }
  }

  protected _transformPlainToInstance<T>(obj: ClassConstructor<T>, rawData: T | T[]): T | T[] {
    if (Array.isArray(rawData)) {
      return rawData.map((e) =>
        plainToInstance(obj, e, {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        }),
      );
    }
    return plainToInstance(obj, rawData, {
      excludeExtraneousValues: true,
      exposeUnsetFields: false,
    });
  }

  protected _interpolateString(template: string, ...values: any[]) {
    return template.replace(/{(\d+)}/g, (_match, index) => {
      return values[index] ?? '';
    });
  }

  //#region AffiliateOrganization
  /**
   * @description Create affiliate organization
   * @param request
   * @param configs
   * @returns
   */
  async createOrganization(
    request: AffiliateOrganizationCreateRequest,
    configs?: HttpClientRequestConfig,
  ): Promise<NewAffiliateOrganizationResponse> {
    const axiosRes = await this.httpClientService.post(AffiliateApiEndpoint.AFFILIATE_ORGANIZATION_CREATE, request, {
      ...configs,
    });
    this._checkExceptionFromAxiosRes(axiosRes);
    return this._transformPlainToInstance(
      NewAffiliateOrganizationResponse,
      axiosRes?.data || null,
    ) as AffiliateOrganizationDto;
  }

  /**
   * @description Update affiliate organization
   * @param request
   * @param configs
   * @returns
   */
  async updateOrganization(
    request: AffiliateOrganizationUpdateRequest,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean> {
    const axiosRes = await this.httpClientService.post(AffiliateApiEndpoint.AFFILIATE_ORGANIZATION_UPDATE, request, {
      ...configs,
    });
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * @description Update affiliate organization status
   * @param request
   * @param configs
   * @returns
   */
  async updateOrganizationStatus(
    request: AffiliateOrganizationUpdateStatusRequest,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean> {
    const axiosRes = await this.httpClientService.put(
      AffiliateApiEndpoint.AFFILIATE_ORGANIZATION_UPDATE_STATUS,
      request,
      {
        ...configs,
      },
    );
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * @description Accept affiliate organization
   * @param request
   * @param configs
   * @returns
   */
  async acceptOrganization(
    request: AffiliateOrganizationAcceptRequest,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean> {
    const axiosRes = await this.httpClientService.put(AffiliateApiEndpoint.AFFILIATE_ORGANIZATION_ACCEPT, request, {
      ...configs,
    });
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * @description Search affiliate organizations
   * @param request
   * @param configs
   * @returns
   */
  async searchOrganizations(
    request: AffiliateOrganizationSearchRequest,
    configs?: HttpClientRequestConfig,
  ): Promise<PagedResultDto<AffiliateOrganizationDtoEs>> {
    const axiosRes = await this.httpClientService.post(AffiliateApiEndpoint.AFFILIATE_ORGANIZATION_ES_SEARCH, request, {
      ...configs,
    });
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  /**
   * @description Get affiliate organization by ID
   * @param id
   * @param configs
   * @returns
   */
  async getOrganizationById(id: string, configs?: HttpClientRequestConfig): Promise<AffiliateOrganizationDtoEs> {
    const axiosRes = await this.httpClientService.get(
      this._interpolateString(AffiliateApiEndpoint.AFFILIATE_ORGANIZATION_ES_GET_BY_ID, id),
      {
        ...configs,
      },
    );
    this._checkExceptionFromAxiosRes(axiosRes);
    return this._transformPlainToInstance(
      AffiliateOrganizationDtoEs,
      axiosRes?.data || null,
    ) as AffiliateOrganizationDtoEs;
  }

  /**
   * @description Get affiliate organization by code
   * @param organizationCode
   * @param configs
   * @returns
   */
  async getOrganizationByCode(
    organizationCode: string,
    configs?: HttpClientRequestConfig,
  ): Promise<AffiliateOrganizationDtoEs> {
    const axiosRes = await this.httpClientService.get(
      this._interpolateString(AffiliateApiEndpoint.AFFILIATE_ORGANIZATION_ES_GET_BY_CODE, organizationCode),
      {
        ...configs,
      },
    );
    this._checkExceptionFromAxiosRes(axiosRes);
    return this._transformPlainToInstance(
      AffiliateOrganizationDtoEs,
      axiosRes?.data || null,
    ) as AffiliateOrganizationDtoEs;
  }
  //#endregion AffiliateOrganization

  //#region AffiliateUser
  /**
   * @description Create affiliate user
   * @param request
   * @param configs
   * @returns
   */
  async createUser(request: AffiliateUserCreateRequest, configs?: HttpClientRequestConfig): Promise<AffiliateUserDto> {
    const axiosRes = await this.httpClientService.post(AffiliateApiEndpoint.AFFILIATE_USER_CREATE, request, {
      ...configs,
    });
    this._checkExceptionFromAxiosRes(axiosRes);
    return this._transformPlainToInstance(AffiliateUserDto, axiosRes?.data || null) as AffiliateUserDto;
  }

  /**
   * @description Update affiliate user
   * @param request
   * @param configs
   * @returns
   */
  async updateUser(request: AffiliateUserUpdateRequest, configs?: HttpClientRequestConfig): Promise<boolean> {
    const axiosRes = await this.httpClientService.post(AffiliateApiEndpoint.AFFILIATE_USER_UPDATE, request, {
      ...configs,
    });
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * @description Update affiliate user status
   * @param request
   * @param configs
   * @returns
   */
  async updateUserStatus(
    request: AffiliateUserUpdateStatusRequest,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean> {
    const axiosRes = await this.httpClientService.post(AffiliateApiEndpoint.AFFILIATE_USER_UPDATE_STATUS, request, {
      ...configs,
    });
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * @description Accept affiliate user
   * @param request
   * @param configs
   * @returns
   */
  async acceptUser(request: AffiliateUserAcceptRequest, configs?: HttpClientRequestConfig): Promise<boolean> {
    const axiosRes = await this.httpClientService.put(AffiliateApiEndpoint.AFFILIATE_USER_ACCEPT, request, {
      ...configs,
    });
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * @description Search affiliate users
   * @param request
   * @param configs
   * @returns
   */
  async searchUsers(
    request: AffiliateUserSearchRequest,
    configs?: HttpClientRequestConfig,
  ): Promise<PagedResultDto<AffiliateUserDto>> {
    const axiosRes = await this.httpClientService.post(AffiliateApiEndpoint.AFFILIATE_USER_ES_SEARCH, request, {
      ...configs,
    });
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  /**
   * @description Get affiliate user by ID
   * @param id
   * @param configs
   * @returns
   */
  async getUserById(id: string, configs?: HttpClientRequestConfig): Promise<AffiliateUserDto> {
    const axiosRes = await this.httpClientService.get(
      this._interpolateString(AffiliateApiEndpoint.AFFILIATE_USER_ES_GET_BY_ID, id),
      {
        ...configs,
      },
    );
    this._checkExceptionFromAxiosRes(axiosRes);
    return this._transformPlainToInstance(AffiliateUserDto, axiosRes?.data || null) as AffiliateUserDto;
  }

  /**
   * @description Get affiliate user by code
   * @param userCode
   * @param configs
   * @returns
   */
  async getUserByCode(userCode: string, configs?: HttpClientRequestConfig): Promise<AffiliateUserDto> {
    const axiosRes = await this.httpClientService.get(
      this._interpolateString(AffiliateApiEndpoint.AFFILIATE_USER_ES_GET_BY_USER_CODE, userCode),
      {
        ...configs,
      },
    );
    this._checkExceptionFromAxiosRes(axiosRes);
    return this._transformPlainToInstance(AffiliateUserDto, axiosRes?.data || null) as AffiliateUserDto;
  }

  /**
   * @description Get affiliate user enums
   * @param configs
   * @returns
   */
  async getUserEnums(configs?: HttpClientRequestConfig): Promise<any> {
    const axiosRes = await this.httpClientService.get(AffiliateApiEndpoint.AFFILIATE_USER_ENUMS, {
      ...configs,
    });
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }
  //#endregion AffiliateUser
}
