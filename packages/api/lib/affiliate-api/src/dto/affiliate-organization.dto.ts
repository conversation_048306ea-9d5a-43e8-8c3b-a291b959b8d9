import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsDate, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { OrganizationStatus } from './common.dto';

export class LegalRepresentativeInfo {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  userCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  representativeName?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  representativePhone?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  representativeEmail?: string;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Expose()
  dateOfBirth?: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  specialization?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  position?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  department?: string;
}

export class AffiliateOrganizationCreateRequest {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  name?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  taxCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  businessField?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  organizationType?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  email?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  phone?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  address?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  ward?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  province?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankName?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankAccountNumber?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankAccountName?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  createdByName?: string;
}

export class AffiliateOrganizationUpdateRequest extends AffiliateOrganizationCreateRequest {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  organizationCode?: string;
}

export class AffiliateOrganizationUpdateStatusRequest {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  organizationCode?: string;

  @ApiProperty({ enum: OrganizationStatus })
  @IsOptional()
  @Expose()
  status?: OrganizationStatus;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  note?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  modifiedBy?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  modifiedByName?: string;
}

export class AffiliateOrganizationAcceptRequest {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  organizationCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  modifiedBy?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  modifiedByName?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  note?: string;
}

export class AffiliateOrganizationSearchRequest {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  keywords?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  organizationCode?: string;

  @ApiProperty({ enum: OrganizationStatus, isArray: true })
  @IsOptional()
  @Expose()
  statuses?: OrganizationStatus[];

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  businessField?: string;

  @ApiProperty({ type: String, isArray: true })
  @IsOptional()
  @Expose()
  organizationTypes?: string[];

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Expose()
  pageSize?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Expose()
  page?: number;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Expose()
  fromDate?: Date;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Expose()
  toDate?: Date;
}

export class NewAffiliateOrganizationResponse {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  organizationCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  name?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  taxCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  businessField?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  organizationType?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  email?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  phone?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  address?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  ward?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  province?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankName?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankAccountNumber?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankAccountName?: string;

  @ApiProperty({ enum: OrganizationStatus })
  @IsOptional()
  @Expose()
  status?: OrganizationStatus;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Expose()
  createdTime?: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  createdByName?: string;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Expose()
  modifiedTime?: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  modifiedBy?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  modifiedByName?: string;
}

export class AffiliateOrganizationDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  organizationCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  name?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  email?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  taxCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  businessField?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  organizationType?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  address?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  ward?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  province?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankName?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankAccountNumber?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankAccountName?: string;

  @ApiProperty({ enum: OrganizationStatus })
  @IsOptional()
  @Expose()
  status?: OrganizationStatus;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Expose()
  joinedAt?: Date;

  @ApiProperty({ type: [LegalRepresentativeInfo] })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => LegalRepresentativeInfo)
  @Expose()
  legalRepresentatives?: LegalRepresentativeInfo[];

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Expose()
  createdTime?: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  createdByName?: string;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Expose()
  modifiedTime?: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  modifiedBy?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  modifiedByName?: string;
}

export class AffiliateOrganizationDtoEs extends AffiliateOrganizationDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  elasticsearchId?: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Expose()
  score?: number;
}
