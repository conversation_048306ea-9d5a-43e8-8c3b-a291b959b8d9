import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsDate, IsNumber, IsOptional, IsString } from 'class-validator';
import { ActorType, AffiliateUserStatus } from './common.dto';

export class AffiliateUserCreateRequest {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  organizationId?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  organizationCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  fullName?: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Expose()
  gender?: number;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Expose()
  dateOfBirth?: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  phoneNumber?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  email?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  identityNumber?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  identityFrontImage?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  identityBackImage?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankName?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankAccountNumber?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankAccountName?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  province?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  ward?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  detailAddress?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  specialization?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  position?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  department?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  partnerType?: string;

  @ApiProperty({ enum: ActorType })
  @IsOptional()
  @IsString()
  @Expose()
  actorType?: ActorType;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  createdByName?: string;
}

export class AffiliateUserUpdateRequest extends AffiliateUserCreateRequest {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  id?: string;
}

export class AffiliateUserUpdateStatusRequest {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  userCode?: string;

  @ApiProperty({ enum: AffiliateUserStatus })
  @IsOptional()
  @IsString()
  @Expose()
  status?: AffiliateUserStatus;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  modifiedBy?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  modifiedByName?: string;
}

export class AffiliateUserAcceptRequest {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  userCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  modifiedBy?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  modifiedByName?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  note?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  organizationId?: string;

  @ApiProperty({ enum: ActorType })
  @IsOptional()
  @IsString()
  @Expose()
  actorType?: ActorType;
}

export class AffiliateUserSearchRequest {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  keywords?: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Expose()
  pageSize?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Expose()
  page?: number;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Expose()
  fromDate?: Date;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Expose()
  toDate?: Date;

  @ApiProperty({ type: String, isArray: true, enum: AffiliateUserStatus })
  @IsOptional()
  @Expose()
  statuses?: AffiliateUserStatus[];

  @ApiProperty({ type: [String] })
  @IsOptional()
  @IsString({ each: true })
  @Expose()
  organizationIds?: string[];
}

export class AffiliateUserDto {
  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  organizationId?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  organizationCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  userCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  fullName?: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  @Expose()
  gender?: number;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Expose()
  dateOfBirth?: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  phoneNumber?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  email?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  identityNumber?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  identityFrontImage?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  identityBackImage?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankName?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankCode?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankAccountNumber?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  bankAccountName?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  province?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  ward?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  detailAddress?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  specialization?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  position?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  department?: string;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Expose()
  joinedAt?: Date;

  @ApiProperty({ enum: AffiliateUserStatus })
  @IsOptional()
  @IsNumber()
  @Expose()
  status?: AffiliateUserStatus;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  partnerType?: string;

  @ApiProperty({ enum: ActorType })
  @IsOptional()
  @IsString()
  @Expose()
  actorType?: ActorType;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Expose()
  createdTime?: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  createdByName?: string;

  @ApiProperty()
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  @Expose()
  modifiedTime?: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  modifiedBy?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  @Expose()
  modifiedByName?: string;
}
