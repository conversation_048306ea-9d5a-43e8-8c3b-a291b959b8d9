import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';

// Enums based on Swagger schema
export enum ActorType {
  Individual = 'Individual',
  OrgRepresentative = 'OrgRepresentative',
}

export enum AffiliateUserStatus {
  Pending = 'Pending',
  Inactive = 'Inactive',
  Active = 'Active',
}

export enum OrganizationStatus {
  Pending = 0,
  Inactive = 1,
  Active = 2,
}

// Paged result DTO based on Volo.Abp.Application.Dtos.PagedResultDto pattern
export class PagedResultDto<T> {
  @ApiProperty()
  @IsOptional()
  @Expose()
  totalCount?: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  items?: T[];
}
