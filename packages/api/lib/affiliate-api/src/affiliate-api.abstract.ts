import { HttpClientRequestConfig } from '../../http-client/src';
import {
  AffiliateOrganizationCreateRequest,
  AffiliateOrganizationUpdateRequest,
  AffiliateOrganizationUpdateStatusRequest,
  AffiliateOrganizationAcceptRequest,
  AffiliateOrganizationSearchRequest,
  AffiliateOrganizationDto,
  NewAffiliateOrganizationResponse,
} from './dto/affiliate-organization.dto';
import {
  AffiliateUserCreateRequest,
  AffiliateUserUpdateRequest,
  AffiliateUserUpdateStatusRequest,
  AffiliateUserAcceptRequest,
  AffiliateUserSearchRequest,
  AffiliateUserDto,
} from './dto/affiliate-user.dto';
import { PagedResultDto } from './dto/common.dto';

export abstract class AffiliateApiAbstract {
  // AffiliateOrganization methods
  abstract createOrganization(
    request: AffiliateOrganizationCreateRequest,
    configs?: HttpClientRequestConfig,
  ): Promise<NewAffiliateOrganizationResponse>;
  abstract updateOrganization(
    request: AffiliateOrganizationUpdateRequest,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean>;
  abstract updateOrganizationStatus(
    request: AffiliateOrganizationUpdateStatusRequest,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean>;
  abstract acceptOrganization(
    request: AffiliateOrganizationAcceptRequest,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean>;
  abstract searchOrganizations(
    request: AffiliateOrganizationSearchRequest,
    configs?: HttpClientRequestConfig,
  ): Promise<PagedResultDto<AffiliateOrganizationDto>>;
  abstract getOrganizationById(id: string, configs?: HttpClientRequestConfig): Promise<AffiliateOrganizationDto>;
  abstract getOrganizationByCode(
    organizationCode: string,
    configs?: HttpClientRequestConfig,
  ): Promise<AffiliateOrganizationDto>;

  // AffiliateUser methods
  abstract createUser(
    request: AffiliateUserCreateRequest,
    configs?: HttpClientRequestConfig,
  ): Promise<AffiliateUserDto>;
  abstract updateUser(request: AffiliateUserUpdateRequest, configs?: HttpClientRequestConfig): Promise<boolean>;
  abstract updateUserStatus(
    request: AffiliateUserUpdateStatusRequest,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean>;
  abstract acceptUser(request: AffiliateUserAcceptRequest, configs?: HttpClientRequestConfig): Promise<boolean>;
  abstract searchUsers(
    request: AffiliateUserSearchRequest,
    configs?: HttpClientRequestConfig,
  ): Promise<PagedResultDto<AffiliateUserDto>>;
  abstract getUserById(id: string, configs?: HttpClientRequestConfig): Promise<AffiliateUserDto>;
  abstract getUserByCode(userCode: string, configs?: HttpClientRequestConfig): Promise<AffiliateUserDto>;
  abstract getUserEnums(configs?: HttpClientRequestConfig): Promise<any>;
}
