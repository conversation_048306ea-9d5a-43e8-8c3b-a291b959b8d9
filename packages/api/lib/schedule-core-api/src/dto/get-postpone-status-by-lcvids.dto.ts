import { Expose } from 'class-transformer';

export class PostponeStatusByLcvIdDto {
  @Expose()
  id: string;

  @Expose()
  lcvId: string;

  @Expose()
  sku: string;

  @Expose()
  orderDetailAttachmentCode: string;

  @Expose()
  appointmentDate: string;

  @Expose()
  postponeStatus: number;

  @Expose()
  orderProperties: number;

  @Expose()
  createdBy: string;

  @Expose()
  createdDate: string;

  @Expose()
  modifiedBy: string;

  @Expose()
  modifiedDate: string;
}
