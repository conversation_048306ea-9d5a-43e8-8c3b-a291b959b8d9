import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MAX_REDIRECT, TIMEOUT } from '../../constants';
import { HttpClientModule } from '../../http-client/src';
import { CamundaApiService } from './camunda-api.service';

@Module({
  imports: [
    HttpClientModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        baseURL: configService.get('CAMUNDA_API_URL'),
        timeout: TIMEOUT,
        maxRedirects: MAX_REDIRECT,
        validateStatus: () => true,
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [CamundaApiService],
  exports: [CamundaApiService],
})
export class CamundaApiModule {}
