import { Injectable } from '@nestjs/common';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import { CamundaApiAbstract } from './camunda-api.abstract';
import { CamundaApiEndpoint } from './camunda-api.endpoint';
import { CancelDepositDto } from './dto/cancel-deposit.dto';
import { _checkExceptionFromAxiosRes } from '../../functions';

@Injectable()
export class CamundaApiService extends CamundaApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  async cancelDeposit(payload: CancelDepositDto, configs?: HttpClientRequestConfig): Promise<boolean> {
    const axiosRes = await this.httpClientService.post(CamundaApiEndpoint.CANCEL_DEPOSIT, payload, configs);
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data === true;
  }
}
