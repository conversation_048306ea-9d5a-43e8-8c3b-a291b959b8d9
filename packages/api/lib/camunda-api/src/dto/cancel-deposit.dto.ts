import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';

export class NewOrderDto {
  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  orderCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  orderAttribute?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  orderChannel?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  createdBy?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  createdDate?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  createdByName?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  orderAttachmentCodes: string[];

  @ApiProperty()
  @Expose()
  @IsOptional()
  phoneNumber: string;
}

export class CancelDepositDto {
  @ApiProperty()
  @Expose()
  @IsString()
  @IsNotEmpty()
  orderCode: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsNotEmpty()
  shopCode: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  modifiedBy?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  modifiedByName?: string;

  @ApiProperty({ type: NewOrderDto })
  @Expose()
  @ValidateNested()
  @Type(() => NewOrderDto)
  @IsOptional()
  newOrder?: NewOrderDto;

  @ApiProperty({
    description: 'Order Chanel Request',
  })
  @IsOptional()
  @Expose()
  @IsString()
  orderChanelRequest?: string;
}
