# HC PBC Patient API

Module này cung cấp các API và utilities để quản lý thông tin bệnh nhân trong hệ thống Healthcare PBC.

## Tính năng chính

- ✅ Interface và DTO cho việc tạo person và patient
- ✅ Transform tự động giữa các model khác nhau
- ✅ Tái sử dụng interface và DTO
- ✅ Validation đầy đủ với class-validator
- ✅ TypeScript support hoàn chỉnh

## Cấu trúc

### Interfaces
- `ICreatePerson`: Interface cơ bản cho việc tạo person
- `ICreatePersonExtended`: Interface mở rộng với các trường patient-specific
- `IAddress`: Interface cho địa chỉ
- `IAuditInfo`: Interface cho thông tin audit

### DTOs
- `CreatePersonDto`: DTO cho việc tạo person
- `CreatePatientRequestDto`: DTO cho request tạo patient
- `CreatePatientFromPersonDto`: DTO mở rộng với transform methods
- `PatientResponseDataDto`: DTO cho response với patientId

### Transform Utilities
- `PatientTransformUtil`: Utility class với các static methods
- `PatientTransformDemo`: Demo class để test transformation

## Cách sử dụng

### 1. Transform từ raw data sang Patient Request

```typescript
import { PatientTransformUtil } from '@frt/nestjs-api';

const rawData = {
  "name": "Ho Huynh Phi",
  "gender": 1,
  "ethnicCode": "01",
  "phoneNumber": "**********",
  "dateOfBirth": "2000-02-11T15:17:32+07:00",
  "email": "",
  "createdBy": "05895",
  "personAddresses": [
    {
      "provinceCode": "23",
      "provinceName": "Hồ Chí Minh",
      "districtCode": "238",
      "districtName": "Huyện Bình Chánh",
      "wardCode": "27625",
      "wardName": "Xã An Phú Tây",
      "address": "123123",
      "status": true,
      "type": 1,
      "createdBy": "05895"
    }
  ],
  "shopCode": "58001"
};

// Transform trực tiếp
const patientRequest = PatientTransformUtil.transformRawDataToPatientRequest(rawData);

// Hoặc transform từng bước
const personDto = PatientTransformUtil.transformToPersonDto(rawData);
const patientRequest2 = PatientTransformUtil.transformPersonToPatient(personDto);
```

### 2. Transform ngược từ Patient Request về Person

```typescript
const personDto = PatientTransformUtil.transformPatientToPerson(patientRequest);
```

### 3. Sử dụng trong Service

```typescript
import { HcPbcPatientApiService, PatientTransformUtil } from '@frt/nestjs-api';

@Injectable()
export class MyService {
  constructor(private patientApiService: HcPbcPatientApiService) {}

  async createPatientFromPersonData(rawPersonData: any) {
    // Transform data
    const patientRequest = PatientTransformUtil.transformRawDataToPatientRequest(rawPersonData);
    
    // Call API
    const result = await this.patientApiService.createPatient(patientRequest);
    
    return result;
  }
}
```

### 4. Validation

Tất cả DTOs đều có validation built-in:

```typescript
import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';

const personDto = plainToInstance(CreatePersonDto, rawData);
const errors = await validate(personDto);

if (errors.length > 0) {
  console.log('Validation errors:', errors);
}
```

## Mapping giữa Person và Patient

| Person Field | Patient Field | Transform Logic |
|--------------|---------------|-----------------|
| `name` | `demographics.name` | Direct mapping |
| `gender` (1/2) | `demographics.gender` | 1→male, 2→female |
| `phoneNumber` | `telecom.phoneNumber` | Direct mapping |
| `email` | `telecom.email` | Direct mapping |
| `personAddresses[type=1]` | `address.frequently` | Filter by type |
| `personAddresses[type=2]` | `address.temporary` | Filter by type |
| `shopCode` | `identifiers.customerId` | Add "KH-" prefix |
| `ethnicCode` | `nationality.ethnicCode` | Direct mapping |
| `guardianName` | `contact[0].name` | If isHaveGuardian=true |

## Demo

Chạy demo để xem cách transform hoạt động:

```typescript
import { PatientTransformDemo } from '@frt/nestjs-api';

const result = PatientTransformDemo.demoTransformation();
console.log(result);
```

## Environment Variables

```env
HC_PBC_PATIENT_API_URL=https://your-patient-api-url.com
```
