import { Injectable } from '@nestjs/common';
import { HttpClientRequestConfig } from '../../http-client/src';
import { CreatePatientRequestDto, CreatePatientRes, SearchPatientByKeywordRequestDto } from './dto';
import { PatientToSearchPersonTransform } from './transforms/search-patient-response.transform';

/**
 * Abstract class defining Healthcare PBC Patient API operations
 * All Patient service implementations must extend this class
 */
@Injectable()
export abstract class HcPbcPatientApiAbstract {
  /**
   * Creates a new patient record
   * @param data - The patient data to create
   * @param configs - Optional HTTP client configuration
   * @returns Promise<CreatePatientResponseDto> - Creation result
   */
  abstract createPatient(data: CreatePatientRequestDto, configs?: HttpClientRequestConfig): Promise<CreatePatientRes>;

  /**
   * Search patients by keyword (name, phone, email, etc.)
   * @param params - Search parameters including keyword and pagination
   * @param configs - Optional HTTP client configuration
   * @returns Promise<SearchPatientByKeywordResponseDto> - Search results
   */
  abstract searchPatientByKeyword(
    params: SearchPatientByKeywordRequestDto,
    configs?: HttpClientRequestConfig,
  ): Promise<PatientToSearchPersonTransform>;
}
