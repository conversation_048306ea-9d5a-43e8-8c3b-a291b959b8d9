import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MAX_REDIRECT, TIMEOUT } from '../../constants';
import { HttpClientModule } from '../../http-client/src';
import { HcPbcPatientApiService } from './hc-pbc-patient-api.service';

/**
 * Healthcare PBC Patient API Module
 * Provides patient management operations
 */
@Module({
  imports: [
    HttpClientModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        baseURL: configService.get('HC_PBC_PATIENT_API_URL'),
        timeout: TIMEOUT,
        maxRedirects: MAX_REDIRECT,
        validateStatus: () => {
          return true;
        },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [HcPbcPatientApiService],
  exports: [HcPbcPatientApiService],
})
export class HcPbcPatientApiModule {}
