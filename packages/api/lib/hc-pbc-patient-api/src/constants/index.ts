/**
 * Gender enumeration for patient demographics
 */
export enum GenderEnum {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
  UNKNOWN = 'unknown',
}

/**
 * Marital status enumeration
 */
export enum MaritalStatusEnum {
  SINGLE = 'single',
  MARRIED = 'married',
  DIVORCED = 'divorced',
  WIDOWED = 'widowed',
  UNKNOWN = 'unknown',
}

/**
 * Contact relationship enumeration
 */
export enum ContactRelationshipEnum {
  PARENT = 'parent',
  GUARDIAN = 'guardian',
  SPOUSE = 'spouse',
  SIBLING = 'sibling',
  CHILD = 'child',
  EMERGENCY = 'emergency',
  OTHER = 'other',
}

/**
 * Age unit enumeration
 */
export enum AgeUnitEnum {
  YEAR = 'Y',
  MONTH = 'M',
  DAY = 'D',
}

/**
 * Relationship code enumeration
 */
export enum RelationshipCodeEnum {
  MOTHER = 'MTH',
  FATHER = 'FTH',
  PATERNAL_GRANDFATHER = 'PGRFTH',
  PATERNAL_GRANDMOTHER = 'PGRMTH',
  MATERNAL_GRANDFATHER = 'MGRFTH',
  MATERNAL_GRANDMOTHER = 'MGRMTH',
  SON = 'SON',
  DAUGHTER = 'DAU',
  WIFE = 'WIFE',
  HUSBAND = 'HUSB',
  FRIEND = 'FRND',
  OTHER = 'OTH',
}

/**
 * Mapping from Vietnamese title names to relationship codes
 */
export const TITLE_NAME_TO_RELATIONSHIP_CODE: Record<string, RelationshipCodeEnum> = {
  Mẹ: RelationshipCodeEnum.MOTHER,
  Bố: RelationshipCodeEnum.FATHER,
  'Ông Nội': RelationshipCodeEnum.PATERNAL_GRANDFATHER,
  'Bà Nội': RelationshipCodeEnum.PATERNAL_GRANDMOTHER,
  'Ông Ngoại': RelationshipCodeEnum.MATERNAL_GRANDFATHER,
  'Bà Ngoại': RelationshipCodeEnum.MATERNAL_GRANDMOTHER,
  'Con trai': RelationshipCodeEnum.SON,
  'Con gái': RelationshipCodeEnum.DAUGHTER,
  Vợ: RelationshipCodeEnum.WIFE,
  Chồng: RelationshipCodeEnum.HUSBAND,
  'Bạn bè': RelationshipCodeEnum.FRIEND,
  Khác: RelationshipCodeEnum.OTHER,
};
