import { Injectable } from '@nestjs/common';
import { _checkExceptionFromAxiosRes, _transformPlainToInstance } from '../../functions';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import { CreatePatientRequestDto, CreatePatientRes, SearchPatientByKeywordRequestDto } from './dto';
import { HcPbcPatientApiAbstract } from './hc-pbc-patient-api.abstract';
import { HcPbcPatientApiEndpoint } from './hc-pbc-patient-api.endpoint';
import { PatientToSearchPersonTransform } from './transforms/search-patient-response.transform';

/**
 * Healthcare PBC Patient API Service
 * Implements patient management operations
 */
@Injectable()
export class HcPbcPatientApiService extends HcPbcPatientApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  /**
   * Creates a new patient record
   * @param data - The patient data to create
   * @param configs - Optional HTTP client configuration
   * @returns Promise<CreatePatientResponseDto> - Creation result
   */
  async createPatient(data: CreatePatientRequestDto, configs?: HttpClientRequestConfig): Promise<CreatePatientRes> {
    const axiosRes = await this.httpClientService.post(HcPbcPatientApiEndpoint.PATIENT_CREATE, data, {
      ...configs,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(CreatePatientRes, axiosRes?.data?.data || null);
  }

  /**
   * Search patients by keyword (name, phone, email, etc.)
   * @param params - Search parameters including keyword and pagination
   * @param configs - Optional HTTP client configuration
   * @returns Promise<SearchPatientByKeywordResponseDto> - Search results
   */
  async searchPatientByKeyword(
    params: SearchPatientByKeywordRequestDto,
    configs?: HttpClientRequestConfig,
  ): Promise<PatientToSearchPersonTransform> {
    const axiosRes = await this.httpClientService.get(HcPbcPatientApiEndpoint.PATIENT_SEARCH_BY_KEYWORD, {
      ...configs,
      params,
    });
    return _transformPlainToInstance(PatientToSearchPersonTransform, axiosRes?.data?.data || null);
  }
}
