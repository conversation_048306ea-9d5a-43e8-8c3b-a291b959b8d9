import { Expose, Transform } from 'class-transformer';
import { CreatePatientRequestDto, CreatePatientRes, PatientDataDto } from '../dto';
import { ICreatePerson, IPerson, FamilyProfileDetail } from './interfaces';
import { GenderEnum, MaritalStatusEnum, ContactRelationshipEnum, AgeUnitEnum } from '../constants';

export class CreatePersonToPatientTransform implements CreatePatientRequestDto {
  @Expose()
  @Transform(({ obj }) => {
    const createPersonInfo: ICreatePerson = obj;

    const patientData: PatientDataDto = {
      // Identifiers
      identifiers: {
        customerId: '', // tạo thông tin customer bên PBC tạo
      },

      shopCode: createPersonInfo?.shopCode,

      // Demographics
      demographics: {
        name: createPersonInfo.name,
        normalizeName: createPersonInfo.name?.toLowerCase().replace(/\s+/g, ' ').trim(),
        gender:
          createPersonInfo.gender === 1
            ? GenderEnum.MALE
            : createPersonInfo.gender === 2
            ? GenderEnum.FEMALE
            : GenderEnum.OTHER,
        active: true,
        dateOfBirth: new Date(createPersonInfo.dateOfBirth).toISOString().split('T')[0],
        maritalStatus: MaritalStatusEnum.SINGLE,
        language: 'vi',
        deceasedBoolean: false,
      },

      // Photo (optional)
      photo: null,

      // Telecom
      telecom: {
        phoneNumber: createPersonInfo?.phoneNumber || '',
        email: createPersonInfo?.email || '',
      },

      // Contact (Guardian)
      contact:
        createPersonInfo.isHaveGuardian && createPersonInfo.guardianName
          ? [
              {
                relationship: ContactRelationshipEnum.PARENT,
                name: createPersonInfo.guardianName,
                phone: createPersonInfo.guardianPhone || '',
                email: '',
                gender: GenderEnum.UNKNOWN,
              },
            ]
          : [],

      // Nationality
      nationality: {
        nationalityCode: 'VN',
        ethnicCode: createPersonInfo.ethnicCode || '01',
      },

      // Address
      address: {
        frequently: createPersonInfo.frequentlyProvinceCode
          ? {
              provinceCode: createPersonInfo.frequentlyProvinceCode,
              provinceName: createPersonInfo.frequentlyProvinceName,
              districtCode: createPersonInfo.frequentlyDistrictCode || '',
              districtName: createPersonInfo.frequentlyDistrictName || '',
              wardCode: createPersonInfo.frequentlyWardCode,
              wardName: createPersonInfo.frequentlyWardName,
              address: createPersonInfo.frequentlyAddress,
            }
          : undefined,
        temporary: createPersonInfo.temporaryProvinceCode
          ? {
              provinceCode: createPersonInfo.temporaryProvinceCode,
              provinceName: createPersonInfo.temporaryProvinceName,
              districtCode: createPersonInfo.temporaryDistrictCode || '',
              districtName: createPersonInfo.temporaryDistrictName || '',
              wardCode: createPersonInfo.temporaryWardCode,
              wardName: createPersonInfo.temporaryWardName,
              address: createPersonInfo.temporaryAddress,
            }
          : undefined,
      },

      // Audit
      audit: {
        source: createPersonInfo.source?.toString() || 'VaccineApp',
        creationTime: new Date().toISOString(),
        modifiedTime: new Date().toISOString(),
        createdBy: createPersonInfo.createdBy || 'system',
        modifiedBy: createPersonInfo.createdBy || 'system',
      },

      // Custom fields
      custom: {
        from: null,
        to: null,
        ageUnit: 'Năm',
        ageUnitCode: AgeUnitEnum.YEAR,
      },
    };

    return patientData;
  })
  patient: PatientDataDto;
}

export class PatientToCreatePersonTransform implements IPerson {
  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData?.patient?.patientId;
  })
  personId: string;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // personContactId is not stored in patient data, return empty string
    return '';
  })
  personContactId: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.identifiers?.lcvId || '';
  })
  lcvId: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.identifiers?.customerId || '';
  })
  customerId: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.identifiers?.nationalVaccineCode || null;
  })
  nationalVaccineCode: any;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.identifiers?.nationalVaccineId || null;
  })
  nationalVaccineId: any;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.identifiers?.identityCard || null;
  })
  identityCard: any;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // jobTitle is not stored in patient data, return null
    return null;
  })
  jobTitle: any;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // isHost is not stored in patient data, return false
    return false;
  })
  isHost: boolean;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.photo?.url || null;
  })
  avatarUrl: any;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // note is not stored in patient data, return null
    return null;
  })
  note: any;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // familyProfileId is not stored in patient data, return empty string
    return '';
  })
  familyProfileId: string;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // titleName is not stored in patient data, return null
    return null;
  })
  titleName: any;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // lastSyncTcqg is not stored in patient data, return null
    return null;
  })
  lastSyncTcqg: any;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.demographics?.active ? 1 : 0;
  })
  status: number;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // referralSourceId is not stored in patient data, return null
    return null;
  })
  referralSourceId: any;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // referralSourceNote is not stored in patient data, return null
    return null;
  })
  referralSourceNote: any;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.demographics?.active || true;
  })
  isActive: boolean;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    const phoneNumber = patientData.patient?.telecom?.phoneNumber;
    return !phoneNumber || phoneNumber.trim() === '';
  })
  nonPhoneCustomer: boolean;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.custom?.from ? new Date(patientData.patient.custom.from).getTime() : 0;
  })
  from: number;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.custom?.to ? new Date(patientData.patient.custom.to).getTime() : 0;
  })
  to: number;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.custom?.ageUnitCode || AgeUnitEnum.YEAR;
  })
  ageUnitCode: number;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.custom?.ageUnit || null;
  })
  ageUnit: any;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.nationality?.nationalityCode || null;
  })
  nationalityCode: any;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // nationalityName is not stored in patient data, return null
    return null;
  })
  nationalityName: any;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // personAddresses is not stored in patient data, return null
    return null;
  })
  personAddresses: any;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.identifiers?.customerId || '';
  })
  id: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    const lcvId = patientData.patient?.identifiers?.lcvId;
    return lcvId ? `LCV-${lcvId}` : '';
  })
  lcvIdDisplayName: string;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // pregnancy is not stored in patient data, return empty array
    return [];
  })
  pregnancy: any[];

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.demographics?.name || '';
  })
  name: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    const gender = patientData.patient?.demographics?.gender;
    if (gender === GenderEnum.MALE) return 1;
    if (gender === GenderEnum.FEMALE) return 2;
    return 0; // OTHER or unknown
  })
  gender: number;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.nationality?.ethnicCode || '01';
  })
  ethnicCode: string;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // ethnicName is not stored in patient data, return empty string
    return '';
  })
  ethnicName: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.telecom?.phoneNumber || '';
  })
  phoneNumber: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.demographics?.dateOfBirth || '';
  })
  dateOfBirth: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.telecom?.email || '';
  })
  email: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    // Check if both frequently and temporary addresses exist and are the same
    const frequently = patientData.patient?.address?.frequently;
    const temporary = patientData.patient?.address?.temporary;

    if (!frequently || !temporary) return false;

    return (
      frequently.provinceCode === temporary.provinceCode &&
      frequently.districtCode === temporary.districtCode &&
      frequently.wardCode === temporary.wardCode &&
      frequently.address === temporary.address
    );
  })
  isSameAddress: boolean;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // familyProfileDetails is not stored in patient data, return empty array
    return [];
  })
  familyProfileDetails: FamilyProfileDetail[];

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.audit?.createdBy || 'system';
  })
  createdBy: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.contact && patientData.patient.contact.length > 0;
  })
  isHaveGuardian: boolean;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.contact && patientData.patient.contact.length > 0
      ? patientData.patient.contact[0].name
      : '';
  })
  guardianName: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.contact && patientData.patient.contact.length > 0
      ? patientData.patient.contact[0].phone
      : '';
  })
  guardianPhone: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.address?.temporary?.provinceCode || '';
  })
  temporaryProvinceCode: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.address?.temporary?.provinceName || '';
  })
  temporaryProvinceName: string;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // TCQG codes are not stored in patient data, return empty string
    return '';
  })
  temporaryTCQGProvinceCode: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.address?.temporary?.districtCode || '';
  })
  temporaryDistrictCode: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.address?.temporary?.districtName || '';
  })
  temporaryDistrictName: string;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // TCQG codes are not stored in patient data, return empty string
    return '';
  })
  temporaryTCQGDistrictCode: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.address?.temporary?.wardCode || '';
  })
  temporaryWardCode: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.address?.temporary?.wardName || '';
  })
  temporaryWardName: string;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // TCQG codes are not stored in patient data, return empty string
    return '';
  })
  temporaryTCQGWardCode: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.address?.temporary?.address || '';
  })
  temporaryAddress: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.address?.frequently?.provinceCode || '';
  })
  frequentlyProvinceCode: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.address?.frequently?.provinceName || '';
  })
  frequentlyProvinceName: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.address?.frequently?.districtCode || '';
  })
  frequentlyDistrictCode: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.address?.frequently?.districtName || '';
  })
  frequentlyDistrictName: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.address?.frequently?.wardCode || '';
  })
  frequentlyWardCode: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.address?.frequently?.wardName || '';
  })
  frequentlyWardName: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    return patientData.patient?.address?.frequently?.address || '';
  })
  frequentlyAddress: string;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // TCQG codes are not stored in patient data, return empty string
    return '';
  })
  frequentlyTCQGDistrictCode: string;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // TCQG codes are not stored in patient data, return empty string
    return '';
  })
  frequentlyTCQGProvinceCode: string;

  @Expose()
  @Transform(({ obj: _obj }) => {
    // TCQG codes are not stored in patient data, return empty string
    return '';
  })
  frequentlyTCQGWardCode: string;

  @Expose()
  @Transform(({ obj }) => {
    const patientData: CreatePatientRes = obj;
    const sourceStr = patientData.patient?.audit?.source;
    if (sourceStr && !isNaN(Number(sourceStr))) {
      return Number(sourceStr);
    }
    return 1; // Default source value
  })
  source: number;

  @Expose()
  @Transform(() => {
    return '';
  })
  shopCode: string;
}
