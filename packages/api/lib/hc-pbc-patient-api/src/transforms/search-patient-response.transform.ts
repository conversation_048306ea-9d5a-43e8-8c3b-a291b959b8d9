import { Expose, plainToInstance, Transform } from 'class-transformer';
import { SearchPatientByKeywordResponseDto } from '../dto';
import { PatientToCreatePersonTransform } from './create-person-to-patient.transform';
import { IPerson, SearchPerson } from './interfaces';

export class PatientToSearchPersonTransform implements SearchPerson {
  @Expose()
  @Transform(({ obj }) => {
    const res: SearchPatientByKeywordResponseDto = obj;
    return res.total;
  })
  totalCount: number;

  @Expose()
  @Transform(({ obj }) => {
    const res: SearchPatientByKeywordResponseDto = obj;
    const arrRes = res.results?.map((e) =>
      plainToInstance(PatientToCreatePersonTransform, e, {
        excludeExtraneousValues: true,
        exposeUnsetFields: false,
      }),
    );
    return arrRes;
  })
  items: IPerson[];
}
