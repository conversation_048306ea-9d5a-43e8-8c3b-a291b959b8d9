import { Expose, Transform } from 'class-transformer';
import { RelatedPersonDto } from '../dto/related-person.dto';
import { GenderEnum, TITLE_NAME_TO_RELATIONSHIP_CODE, RelationshipCodeEnum } from '../constants';

export interface FamilyProfileDetail {
  name: string;
  phoneNumber: string;
  gender: number;
  titleId: string;
  titleName: string;
  lcvId: string | null;
  customerId: string | null;
  isAdding: boolean;
  source: number;
}

export class FamilyProfileToRelatedPersonTransform {
  @Expose()
  @Transform(({ obj }) => {
    const familyDetails: FamilyProfileDetail[] = obj.familyProfileDetails || [];

    return familyDetails.map(
      (detail): RelatedPersonDto => ({
        identifiers: {
          lcvId: '',
          customerId: '',
          contactId: '',
        },
        demographics: {
          name: detail?.name,
          nickname: '',
          gender: detail?.gender === 1 ? GenderEnum.FEMALE : detail?.gender === 0 ? GenderEnum.MALE : GenderEnum.OTHER,
          relationshipCode: [TITLE_NAME_TO_RELATIONSHIP_CODE[detail?.titleName] || RelationshipCodeEnum.OTHER],
          active: true,
        },
        telecom: {
          phoneNumber: detail?.phoneNumber,
          email: '',
        },
        photo: {
          url: '',
        },
        custom: {
          suggest: detail?.source.toString(),
        },
        audit: {
          source: detail?.source?.toString() || 'VaccineApp',
          creationTime: new Date().toISOString(),
          modifiedTime: new Date().toISOString(),
          createdBy: obj['createdBy'] || 'system',
          modifiedBy: obj['createdBy'] || 'system',
        },
      }),
    );
  })
  relatedPersons: RelatedPersonDto[];
}
