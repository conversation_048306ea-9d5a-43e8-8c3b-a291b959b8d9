export interface IPerson {
  personId: string;
  personContactId: string;
  lcvId: string;
  customerId: string;
  name: string;
  nationalVaccineCode: any;
  nationalVaccineId: any;
  identityCard: any;
  dateOfBirth: string;
  gender: number;
  phoneNumber: string;
  jobTitle: any;
  email: any;
  frequentlyProvinceCode: string;
  frequentlyProvinceName: string;
  frequentlyDistrictCode: string;
  frequentlyDistrictName: string;
  frequentlyWardCode: string;
  frequentlyWardName: string;
  frequentlyAddress: string;
  temporaryProvinceCode: string;
  temporaryProvinceName: string;
  temporaryDistrictCode: string;
  temporaryDistrictName: string;
  temporaryWardCode: string;
  temporaryWardName: string;
  temporaryAddress: string;
  ethnicCode: string;
  ethnicName: any;
  isHost: boolean;
  guardianName: string;
  guardianPhone: string;
  avatarUrl: any;
  isSameAddress: boolean;
  isHaveGuardian: boolean;
  note: any;
  familyProfileId: string;
  titleName: any;
  lastSyncTcqg: any;
  status: number;
  referralSourceId: any;
  referralSourceNote: any;
  source: number;
  isActive: boolean;
  nonPhoneCustomer: boolean;
  from: number;
  to: number;
  ageUnitCode: number;
  ageUnit: any;
  nationalityCode: any;
  nationalityName: any;
  personAddresses: any;
  id: string;
  lcvIdDisplayName: string;
  pregnancy: any[];
  familyProfileDetails: FamilyProfileDetail[];
}

export interface FamilyProfileDetail {
  lcvId: string;
  titleId: string;
  personId: string;
  name: string;
  nationalVaccineCode: any;
  nationalVaccineId: any;
  dateOfBirth: string;
  customerId: string;
  gender: number;
  phoneNumber: string;
  identityCard: any;
  familyProfileId: string;
  titleName: string;
  from: number;
  to: number;
  ageUnitCode: number;
  ageUnit: any;
  isHost: boolean;
  isNewInsert: boolean;
}

export interface IPersonAddress {
  provinceCode: string;
  provinceName: string;
  districtCode?: string;
  districtName?: string;
  wardCode: string;
  wardName: string;
  address: string;
  isAddressChange?: boolean;
  status: boolean;
  isNew: boolean;
  type: number;
  createdBy: string;
}
