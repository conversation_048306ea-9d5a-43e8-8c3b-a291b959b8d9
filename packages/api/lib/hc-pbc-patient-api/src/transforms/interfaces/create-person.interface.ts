import { FamilyProfileDetail } from './person.interface';

export interface ICreatePerson {
  name: string;
  gender: number;
  ethnicCode: string;
  ethnicName: string;
  phoneNumber: string;
  dateOfBirth: string;
  email: string;
  isSameAddress: boolean;
  familyProfileDetails: FamilyProfileDetail[];
  createdBy: string;
  isHaveGuardian: boolean;
  guardianName: string;
  guardianPhone: string;
  temporaryProvinceCode: string;
  temporaryProvinceName: string;
  temporaryTCQGProvinceCode: string;
  temporaryDistrictCode: string;
  temporaryDistrictName: string;
  temporaryTCQGDistrictCode: string;
  temporaryWardCode: string;
  temporaryWardName: string;
  temporaryTCQGWardCode: string;
  temporaryAddress: string;
  frequentlyProvinceCode: string;
  frequentlyProvinceName: string;
  frequentlyDistrictCode: string;
  frequentlyDistrictName: string;
  frequentlyWardCode: string;
  frequentlyWardName: string;
  frequentlyAddress: string;
  frequentlyTCQGDistrictCode: string;
  frequentlyTCQGProvinceCode: string;
  frequentlyTCQGWardCode: string;

  // personAddresses: IPersonAddress[];
  source: number;
  shopCode: string;
}
