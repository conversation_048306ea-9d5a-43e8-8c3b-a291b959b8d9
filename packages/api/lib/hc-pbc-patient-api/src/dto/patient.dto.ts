import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
  IsArray,
  IsOptional,
  IsString,
  IsBoolean,
  IsEmail,
  IsDateString,
  IsEnum,
  ValidateNested,
  Matches,
} from 'class-validator';
import { GenderEnum, MaritalStatusEnum, ContactRelationshipEnum, AgeUnitEnum } from '../constants';

/**
 * Patient Identifiers DTO
 */
export class PatientIdentifiersDto {
  @ApiProperty({ description: 'Identity card number', example: '123456789012', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  identityCard?: string;

  @ApiProperty({ description: 'Insurance number', example: 'BHYT123456789', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  insuranceNumber?: string;

  @ApiProperty({ description: 'LCV ID', example: 'LC123456', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  lcvId?: string;

  @ApiProperty({ description: 'Short LCV ID', example: '283929389', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  shortLcvId?: string;

  @ApiProperty({ description: 'Customer ID', example: 'KH-0001', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  customerId?: string;

  @ApiProperty({ description: 'National vaccine ID', example: '0001', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  nationalVaccineId?: string;

  @ApiProperty({ description: 'National vaccine code', example: 'VAC-001', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  nationalVaccineCode?: string;

  @ApiProperty({ description: 'EMR code', example: 'EMR-0029', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  emrCode?: string;
}

/**
 * Patient Demographics DTO
 */
export class PatientDemographicsDto {
  @ApiProperty({ description: 'Patient name', example: 'Nguyễn Văn A' })
  @IsString()
  @Expose()
  name: string;

  @ApiProperty({ description: 'Normalized name', example: 'nguyen vana', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  normalizeName?: string;

  @ApiProperty({ description: 'Gender', enum: GenderEnum, example: GenderEnum.MALE })
  @IsEnum(GenderEnum)
  @Expose()
  gender: GenderEnum;

  @ApiProperty({ description: 'Active status', example: true })
  @IsBoolean()
  @Expose()
  active: boolean;

  @ApiProperty({ description: 'Date of birth', example: '2015-06-01' })
  @IsDateString()
  @Expose()
  dateOfBirth: string;

  @ApiProperty({ description: 'Marital status', enum: MaritalStatusEnum, example: MaritalStatusEnum.SINGLE })
  @IsEnum(MaritalStatusEnum)
  @Expose()
  maritalStatus: MaritalStatusEnum;

  @ApiProperty({ description: 'Language code', example: 'vi' })
  @IsString()
  @Expose()
  language: string;

  @ApiProperty({ description: 'Deceased status', example: false })
  @IsBoolean()
  @Expose()
  deceasedBoolean: boolean;
}

/**
 * Patient Photo DTO
 */
export class PatientPhotoDto {
  @ApiProperty({ description: 'Photo URL', example: 'https://cdn.app/avatarA.png' })
  @IsString()
  @Expose()
  url: string;
}

/**
 * Patient Telecom DTO
 */
export class PatientTelecomDto {
  @ApiProperty({ description: 'Phone number', example: '**********' })
  @IsString()
  @Matches(/^[0-9+\-\s()]+$/, { message: 'Invalid phone number format' })
  @Expose()
  phoneNumber: string;

  @ApiProperty({ description: 'Email address', example: '<EMAIL>' })
  @IsEmail()
  @Expose()
  email: string;
}

/**
 * Patient Contact DTO
 */
export class PatientContactDto {
  @ApiProperty({
    description: 'Relationship to patient',
    enum: ContactRelationshipEnum,
    example: ContactRelationshipEnum.PARENT,
  })
  @IsEnum(ContactRelationshipEnum)
  @Expose()
  relationship: ContactRelationshipEnum;

  @ApiProperty({ description: 'Contact name', example: 'Nguyễn Văn B' })
  @IsString()
  @Expose()
  name: string;

  @ApiProperty({ description: 'Contact phone', example: '**********' })
  @IsString()
  @Matches(/^[0-9+\-\s()]+$/, { message: 'Invalid phone number format' })
  @Expose()
  phone: string;

  @ApiProperty({ description: 'Contact email', example: '<EMAIL>' })
  @IsEmail()
  @Expose()
  email: string;

  @ApiProperty({ description: 'Contact gender', enum: GenderEnum, example: GenderEnum.MALE })
  @IsEnum(GenderEnum)
  @Expose()
  gender: GenderEnum;
}

/**
 * Patient Nationality DTO
 */
export class PatientNationalityDto {
  @ApiProperty({ description: 'Nationality code', example: 'VN' })
  @IsString()
  @Expose()
  nationalityCode: string;

  @ApiProperty({ description: 'Ethnic code', example: '01' })
  @IsString()
  @Expose()
  ethnicCode: string;
}

/**
 * Address DTO
 */
export class AddressDetailsDto {
  @ApiProperty({ description: 'Province code', example: '79' })
  @IsString()
  @Expose()
  provinceCode: string;

  @ApiProperty({ description: 'Province name', example: 'TP.HCM' })
  @IsString()
  @Expose()
  provinceName: string;

  @ApiProperty({ description: 'District code', example: '001' })
  @IsString()
  @Expose()
  districtCode: string;

  @ApiProperty({ description: 'District name', example: 'Quận 1' })
  @IsString()
  @Expose()
  districtName: string;

  @ApiProperty({ description: 'Ward code', example: '00123' })
  @IsString()
  @Expose()
  wardCode: string;

  @ApiProperty({ description: 'Ward name', example: 'Phường Bến Nghé' })
  @IsString()
  @Expose()
  wardName: string;

  @ApiProperty({ description: 'Detailed address', example: '12 Nguyễn Huệ' })
  @IsString()
  @Expose()
  address: string;
}

/**
 * Patient Address DTO
 */
export class PatientAddressDto {
  @ApiProperty({ description: 'Frequently used address', type: AddressDetailsDto })
  @ValidateNested()
  @Type(() => AddressDetailsDto)
  @Expose()
  frequently: AddressDetailsDto;

  @ApiProperty({ description: 'Temporary address', type: AddressDetailsDto })
  @ValidateNested()
  @Type(() => AddressDetailsDto)
  @Expose()
  temporary: AddressDetailsDto;
}

/**
 * Patient Audit DTO
 */
export class PatientAuditDto {
  @ApiProperty({ description: 'Source system', example: 'VaccineApp' })
  @IsString()
  @Expose()
  source: string;

  @ApiProperty({ description: 'Creation time', example: '2025-06-26T10:00:00Z' })
  @IsDateString()
  @Expose()
  creationTime: string;

  @ApiProperty({ description: 'Modified time', example: '2025-06-26T10:30:00Z' })
  @IsDateString()
  @Expose()
  modifiedTime: string;

  @ApiProperty({ description: 'Created by', example: 'staff-01' })
  @IsString()
  @Expose()
  createdBy: string;

  @ApiProperty({ description: 'Modified by', example: 'staff-01' })
  @IsString()
  @Expose()
  modifiedBy: string;
}

/**
 * Patient Custom Fields DTO
 */
export class PatientCustomDto {
  @ApiProperty({ description: 'From date', required: false })
  @IsOptional()
  @IsDateString()
  @Expose()
  from?: string | null;

  @ApiProperty({ description: 'To date', required: false })
  @IsOptional()
  @IsDateString()
  @Expose()
  to?: string | null;

  @ApiProperty({ description: 'Age unit display', example: 'Năm' })
  @IsString()
  @Expose()
  ageUnit: string;

  @ApiProperty({ description: 'Age unit code', enum: AgeUnitEnum, example: AgeUnitEnum.YEAR })
  @IsEnum(AgeUnitEnum)
  @Expose()
  ageUnitCode: AgeUnitEnum;
}

/**
 * Patient Data DTO - Main patient object
 */
export class PatientDataDto {
  @ApiProperty()
  @IsOptional()
  @Expose()
  shopCode?: string;

  @ApiProperty({ description: 'Patient identifiers', type: PatientIdentifiersDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => PatientIdentifiersDto)
  @Expose()
  identifiers?: PatientIdentifiersDto;

  @ApiProperty({ description: 'Patient demographics', type: PatientDemographicsDto })
  @ValidateNested()
  @Type(() => PatientDemographicsDto)
  @Expose()
  demographics: PatientDemographicsDto;

  @ApiProperty({ description: 'Patient photo', type: PatientPhotoDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => PatientPhotoDto)
  @Expose()
  photo?: PatientPhotoDto;

  @ApiProperty({ description: 'Patient telecom information', type: PatientTelecomDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => PatientTelecomDto)
  @Expose()
  telecom?: PatientTelecomDto;

  @ApiProperty({
    description: 'Patient contacts',
    type: [PatientContactDto],
    required: false,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PatientContactDto)
  @Expose()
  contact?: PatientContactDto[];

  @ApiProperty({ description: 'Patient nationality', type: PatientNationalityDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => PatientNationalityDto)
  @Expose()
  nationality?: PatientNationalityDto;

  @ApiProperty({ description: 'Patient address', type: PatientAddressDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => PatientAddressDto)
  @Expose()
  address?: PatientAddressDto;

  @ApiProperty({ description: 'Audit information', type: PatientAuditDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => PatientAuditDto)
  @Expose()
  audit?: PatientAuditDto;

  @ApiProperty({ description: 'Custom fields', type: PatientCustomDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => PatientCustomDto)
  @Expose()
  custom?: PatientCustomDto;
}

/**
 * Patient Response Data DTO - Patient object with ID for response
 */
export class PatientResponseDataDto extends PatientDataDto {
  @ApiProperty({ description: 'Patient ID', example: 'af68caa42a77c1e069a8b0525915af87' })
  @IsString()
  @Expose()
  patientId: string;
}
