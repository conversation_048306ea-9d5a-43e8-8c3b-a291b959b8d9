import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString, Min } from 'class-validator';
import { PatientResponseDataDto } from './patient.dto';

/**
 * Search Patient By Keyword Request DTO
 */
export class SearchPatientByKeywordRequestDto {
  @ApiProperty({
    description: 'Search keyword (name, phone, email, etc.)',
    example: 'Vũ Ngọc Thắng',
  })
  @IsString()
  @IsNotEmpty()
  @Expose()
  keyword: string;

  @ApiProperty({
    description: 'Number of results per page',
    example: 20,
    required: false,
  })
  @IsOptional()
  @Expose()
  size?: number;

  @ApiProperty({
    description: 'Starting index for pagination (0-based)',
    example: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Expose()
  @Transform(({ obj }) => {
    const pageNumber = parseInt(obj?.from) || 1;
    const pageSize = parseInt(obj?.size) || 20;
    return Math.max(0, (pageNumber - 1) * pageSize);
  })
  from?: number = 0;

  @ApiProperty({
    description: 'Field to sort by',
    example: '_score',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  @Transform(({ obj }) => obj['sorting'])
  sortBy?: string = '_score';

  @ApiProperty({
    description: 'Sort order (asc/desc)',
    example: 'desc',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Expose()
  sortOrder?: string = 'desc';
}

/**
 * Search Patient By Keyword Response DTO
 */
export class SearchPatientByKeywordResponseDto {
  @ApiProperty({
    description: 'List of patients matching the keyword',
    type: [SearchPatientByKeywordResponseDto],
  })
  @Type(() => SearchPatientByKeywordResponseDto)
  @Expose()
  @IsNumber()
  total: number;

  @IsArray()
  @Expose()
  results: PatientResponseDataDto[];
}
