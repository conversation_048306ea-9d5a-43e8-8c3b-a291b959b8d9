import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';
import { PatientDataDto, PatientResponseDataDto } from './patient.dto';
import { RelatedPersonDto } from './related-person.dto';

/**
 * Patient Data DTO - Main patient object
 */
// export class PatientDataDto {
//   @ApiProperty()
//   @IsOptional()
//   @Expose()
//   shopCode?: string;

//   @ApiProperty({ description: 'Patient identifiers', type: PatientIdentifiersDto, required: false })
//   @IsOptional()
//   @ValidateNested()
//   @Type(() => PatientIdentifiersDto)
//   @Expose()
//   identifiers?: PatientIdentifiersDto;

//   @ApiProperty({ description: 'Patient demographics', type: PatientDemographicsDto })
//   @ValidateNested()
//   @Type(() => PatientDemographicsDto)
//   @Expose()
//   demographics: PatientDemographicsDto;

//   @ApiProperty({ description: 'Patient photo', type: PatientPhotoDto, required: false })
//   @IsOptional()
//   @ValidateNested()
//   @Type(() => PatientPhotoDto)
//   @Expose()
//   photo?: PatientPhotoDto;

//   @ApiProperty({ description: 'Patient telecom information', type: PatientTelecomDto, required: false })
//   @IsOptional()
//   @ValidateNested()
//   @Type(() => PatientTelecomDto)
//   @Expose()
//   telecom?: PatientTelecomDto;

//   @ApiProperty({
//     description: 'Patient contacts',
//     type: [PatientContactDto],
//     required: false,
//     isArray: true,
//   })
//   @IsOptional()
//   @IsArray()
//   @ValidateNested({ each: true })
//   @Type(() => PatientContactDto)
//   @Expose()
//   contact?: PatientContactDto[];

//   @ApiProperty({ description: 'Patient nationality', type: PatientNationalityDto, required: false })
//   @IsOptional()
//   @ValidateNested()
//   @Type(() => PatientNationalityDto)
//   @Expose()
//   nationality?: PatientNationalityDto;

//   @ApiProperty({ description: 'Patient address', type: PatientAddressDto, required: false })
//   @IsOptional()
//   @ValidateNested()
//   @Type(() => PatientAddressDto)
//   @Expose()
//   address?: PatientAddressDto;

//   @ApiProperty({ description: 'Audit information', type: PatientAuditDto, required: false })
//   @IsOptional()
//   @ValidateNested()
//   @Type(() => PatientAuditDto)
//   @Expose()
//   audit?: PatientAuditDto;

//   @ApiProperty({ description: 'Custom fields', type: PatientCustomDto, required: false })
//   @IsOptional()
//   @ValidateNested()
//   @Type(() => PatientCustomDto)
//   @Expose()
//   custom?: PatientCustomDto;
// }

/**
 * Patient Response Data DTO - Patient object with ID for response
 */
// export class PatientResponseDataDto extends PatientDataDto {
//   @ApiProperty({ description: 'Patient ID', example: 'af68caa42a77c1e069a8b0525915af87' })
//   @IsString()
//   @Expose()
//   patientId: string;
// }

/**
 * Create Patient Request DTO - Root request object
 */
export class CreatePatientRequestDto {
  @ApiProperty({ description: 'Patient data', type: PatientDataDto })
  @ValidateNested()
  @Type(() => PatientDataDto)
  @Expose()
  patient: PatientDataDto;

  @ApiProperty({ description: 'Related person information', type: RelatedPersonDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => RelatedPersonDto)
  @Expose()
  relatedPerson?: RelatedPersonDto;
}

/**
 * Create Patient Response DTO
 */
export class CreatePatientRes {
  @ApiProperty({ description: 'Patient data with ID', type: PatientResponseDataDto })
  @ValidateNested()
  @Type(() => PatientResponseDataDto)
  @Expose()
  patient: PatientResponseDataDto;
}
