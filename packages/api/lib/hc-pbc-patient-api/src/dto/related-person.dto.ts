import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsBoolean, IsEnum, IsOptional, IsString, ValidateNested } from 'class-validator';
import { PatientIdentifiersDto } from './patient.dto';
import { GenderEnum } from '../constants';

export class RelatedPersonIdentifiersDto extends PatientIdentifiersDto {
  @ApiProperty({ description: 'Contact ID', example: 'REL001', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  contactId?: string;
}

export class RelatedPersonPatientReferenceDto {
  @ApiProperty({ description: 'Patient ID', example: 'c8f5e622-8a90-4b13-90d9-657edf7b6bba' })
  @IsString()
  @Expose()
  patientId: string;
}

export class RelatedPersonDemographicsDto {
  @ApiProperty({ description: 'Related person name', example: '<PERSON><PERSON><PERSON><PERSON>h<PERSON>' })
  @IsString()
  @Expose()
  name: string;

  @ApiProperty({ description: 'Nickname', example: 'Mẹ Bé A', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  nickname?: string;

  @ApiProperty({ description: 'Gender', enum: GenderEnum, example: GenderEnum.FEMALE })
  @IsEnum(GenderEnum)
  @Expose()
  gender: GenderEnum;

  @ApiProperty({ description: 'Relationship codes', example: ['mother'], isArray: true })
  @IsArray()
  @IsString({ each: true })
  @Expose()
  relationshipCode: string[];

  @ApiProperty({ description: 'Active status', example: true })
  @IsBoolean()
  @Expose()
  active: boolean;
}

export class RelatedPersonTelecomDto {
  @ApiProperty({ description: 'Phone number', example: '**********', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  phoneNumber?: string;

  @ApiProperty({ description: 'Email address', example: '<EMAIL>', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  email?: string;
}

export class RelatedPersonPhotoDto {
  @ApiProperty({ description: 'Photo URL', example: 'https://cdn.app/avatarB.png', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  url?: string;
}

export class RelatedPersonCustomDto {
  @ApiProperty({ description: 'Suggest flag', example: '1', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  suggest?: string;
}

export class RelatedPersonAuditDto {
  @ApiProperty({ description: 'Creation time', example: '2025-06-26T10:20:00Z', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  creationTime?: string;

  @ApiProperty({ description: 'Created by', example: 'staff-02', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  createdBy?: string;

  @ApiProperty({ description: 'Modified time', example: '2025-06-26T10:35:00Z', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  modifiedTime?: string;

  @ApiProperty({ description: 'Modified by', example: 'staff-02', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  modifiedBy?: string;

  @ApiProperty({ description: 'Source system', example: 'VaccineApp', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  source?: string;
}

export class RelatedPersonDto {
  @ApiProperty({ description: 'Related person ID', example: 'c8f5e622-8a90-4b13-90d9-657edf7b6uei', required: false })
  @IsOptional()
  @IsString()
  @Expose()
  relatedPersonId?: string;

  @ApiProperty({ description: 'Related person identifiers', type: RelatedPersonIdentifiersDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => RelatedPersonIdentifiersDto)
  @Expose()
  identifiers?: RelatedPersonIdentifiersDto;

  @ApiProperty({ description: 'Patient reference', type: RelatedPersonPatientReferenceDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => RelatedPersonPatientReferenceDto)
  @Expose()
  patientReference?: RelatedPersonPatientReferenceDto;

  @ApiProperty({ description: 'Demographics information', type: RelatedPersonDemographicsDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => RelatedPersonDemographicsDto)
  @Expose()
  demographics?: RelatedPersonDemographicsDto;

  @ApiProperty({ description: 'Telecom information', type: RelatedPersonTelecomDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => RelatedPersonTelecomDto)
  @Expose()
  telecom?: RelatedPersonTelecomDto;

  @ApiProperty({ description: 'Photo information', type: RelatedPersonPhotoDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => RelatedPersonPhotoDto)
  @Expose()
  photo?: RelatedPersonPhotoDto;

  @ApiProperty({ description: 'Custom fields', type: RelatedPersonCustomDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => RelatedPersonCustomDto)
  @Expose()
  custom?: RelatedPersonCustomDto;

  @ApiProperty({ description: 'Audit information', type: RelatedPersonAuditDto, required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => RelatedPersonAuditDto)
  @Expose()
  audit?: RelatedPersonAuditDto;
}
