import { IsOptional, IsString, IsInt, IsDateString, IsEnum, ValidateIf } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ApplyRoleEnum, OrderPropertiesEnum } from '../enum';
import { Expose } from 'class-transformer';

export class RewardPointRuleCalculatorByOdacRequest {
  @ApiPropertyOptional({ description: 'SKU code' })
  @IsOptional()
  @IsString()
  sku?: string;

  @ApiProperty({ description: 'Unit code', format: 'int32' })
  @IsInt()
  unitCode: number;

  @ApiPropertyOptional({ description: 'Date completed' })
  @IsOptional()
  @IsDateString()
  dateCompleted?: string;

  @ApiPropertyOptional({ description: 'Order attribute' })
  @IsOptional()
  @IsInt()
  orderAttribute?: number;

  @ApiProperty({ description: 'Order properties', enum: OrderPropertiesEnum })
  @IsEnum(OrderPropertiesEnum)
  orderProperties: OrderPropertiesEnum;

  @ApiProperty({ description: 'Apply role', enum: ApplyRoleEnum })
  @IsEnum(ApplyRoleEnum)
  applyRole: ApplyRoleEnum;

  @ApiPropertyOptional({ description: 'Transaction type', type: Number })
  @IsOptional()
  @IsInt()
  transactionType?: number;

  @ApiPropertyOptional({ description: 'Employee code' })
  @IsOptional()
  @IsString()
  employeeCode?: string;

  @ApiPropertyOptional({ description: 'Quantity', type: Number })
  @IsOptional()
  @IsInt()
  quantity?: number;

  @ApiPropertyOptional({ description: 'Is package', type: Boolean })
  @IsOptional()
  isPackage?: boolean;

  @ApiPropertyOptional({ description: 'Is odd', type: Boolean })
  @IsOptional()
  isOdd?: boolean;

  @ApiPropertyOptional({ description: 'ODAC', type: String })
  @IsOptional()
  @IsString()
  odac?: string;
}

export class RewardPointRuleCalculatorByOdacResponse {
  @ApiProperty({ description: 'Reward rule code', type: String })
  @Expose()
  rewardRuleCode: string;

  @ApiProperty({ description: 'Reward value', type: Number })
  @Expose()
  rewardValue: number;

  @ApiProperty({ description: 'Reward type', type: Number })
  @Expose()
  rewardType: number;

  @ApiProperty({ description: 'SKU', type: String })
  @Expose()
  sku: string;

  @ApiProperty({ description: 'Apply role', type: Number })
  @Expose()
  applyRole: number;

  @ApiProperty({ description: 'Employee role code', type: String })
  @Expose()
  employeeRoleCode: string;

  @ApiProperty({ description: 'Employee role name', type: String })
  @Expose()
  employeeRoleName: string;

  @ApiProperty({ description: 'ODAC', type: String })
  @Expose()
  odac: string;
}

export class RewardPointRuleCalculatorByQuantityRequest {
  @ApiPropertyOptional({ description: 'SKU code' })
  @IsOptional()
  @IsString()
  sku?: string;

  @ApiPropertyOptional({ description: 'Unit code', format: 'int32' })
  @IsOptional()
  @IsInt()
  unitCode?: number;

  @ApiPropertyOptional({ description: 'Date completed' })
  @IsOptional()
  @IsDateString()
  dateCompleted?: string;

  @ApiPropertyOptional({ description: 'Order attribute' })
  @IsOptional()
  @IsInt()
  orderAttribute?: number;

  @ApiPropertyOptional({ description: 'Order properties', enum: OrderPropertiesEnum })
  @IsOptional()
  @IsEnum(OrderPropertiesEnum)
  orderProperties?: OrderPropertiesEnum;

  @ApiPropertyOptional({ description: 'Apply role', enum: ApplyRoleEnum })
  @IsOptional()
  @IsEnum(ApplyRoleEnum)
  applyRole?: ApplyRoleEnum;

  @ApiPropertyOptional({ description: 'Transaction type', type: Number })
  @IsOptional()
  @IsInt()
  transactionType?: number;

  @ApiPropertyOptional({ description: 'Employee code' })
  @IsOptional()
  @IsString()
  employeeCode?: string;

  @ApiPropertyOptional({ description: 'Quantity', type: Number })
  @IsOptional()
  @IsInt()
  quantity?: number;

  @ApiPropertyOptional({ description: 'Order step', type: Number })
  @IsOptional()
  @IsInt()
  orderStep?: number;

  @ApiPropertyOptional({ description: 'Reward rule type', type: String })
  @IsOptional()
  @IsString()
  rewardRuleType?: string;

  @ApiPropertyOptional({ description: 'Reward rule scheme', type: String })
  @IsOptional()
  @IsString()
  rewardRuleScheme?: string;

  @ApiPropertyOptional({ description: 'Order channel', oneOf: [{ type: 'string' }, { type: 'number' }] })
  @IsOptional()
  @ValidateIf((o) => typeof o.orderChannel === 'string')
  @IsString()
  @ValidateIf((o) => typeof o.orderChannel === 'number')
  @IsInt()
  orderChannel?: string | number;
}

export class RewardPointRuleCalculatorByQuantityResponseItem {
  @ApiProperty({ description: 'Reward rule code', type: String })
  @Expose()
  rewardRuleCode: string;

  @ApiProperty({ description: 'Reward value', type: Number })
  @Expose()
  rewardValue: number;

  @ApiProperty({ description: 'Reward type', type: Number })
  @Expose()
  rewardType: number;

  @ApiProperty({ description: 'SKU', type: String })
  @Expose()
  sku: string;

  @ApiProperty({ description: 'Apply role', type: Number })
  @Expose()
  applyRole: number;

  @ApiProperty({ description: 'Employee role code', type: String })
  @Expose()
  employeeRoleCode: string;

  @ApiProperty({ description: 'Employee role name', type: String })
  @Expose()
  employeeRoleName: string;

  @ApiProperty({ description: 'Label', type: String })
  @Expose()
  label: string;

  @ApiProperty({ description: 'Reward rule type', type: String })
  @Expose()
  rewardRuleType: string;

  @ApiProperty({ description: 'Reward rule scheme', type: String })
  @Expose()
  rewardRuleScheme: string;

  @ApiProperty({ description: 'Order channel', oneOf: [{ type: 'string' }, { type: 'number' }] })
  @Expose()
  orderChannel: string | number;

  @ApiProperty({ description: 'Order step', type: Number })
  @Expose()
  orderStep: number;
}
