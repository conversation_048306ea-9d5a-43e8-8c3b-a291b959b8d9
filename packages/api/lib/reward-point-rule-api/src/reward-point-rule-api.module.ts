import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MAX_REDIRECT, TIMEOUT } from '../../constants';
import { HttpClientModule } from '../../http-client/src';
import { RewardPointRuleApiService } from './reward-point-rule-api.service';

@Module({
  imports: [
    HttpClientModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        baseURL:
          configService.get('REWARD_POINT_RULE_API_URL') || configService.get('VACCINE_REWARD_POINT_RULE_API_URL'),
        timeout: TIMEOUT,
        maxRedirects: MAX_REDIRECT,
        validateStatus: () => {
          return true;
        },
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [RewardPointRuleApiService],
  exports: [RewardPointRuleApiService],
})
export class RewardPointRuleApiModule {}
