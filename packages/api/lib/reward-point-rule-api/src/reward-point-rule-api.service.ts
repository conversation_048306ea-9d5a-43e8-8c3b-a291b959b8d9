import { Injectable } from '@nestjs/common';
import { _checkExceptionFromAxiosRes } from '../../functions';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import {
  RewardPointRuleCalculatorByOdacRequest,
  RewardPointRuleCalculatorByOdacResponse,
  RewardPointRuleCalculatorByQuantityRequest,
  RewardPointRuleCalculatorByQuantityResponseItem,
} from './dto';
import { RewardPointRuleApiAbstract } from './reward-point-rule-api.abstract';
import { RewardPointRuleApiEndpoint } from './reward-point-rule-api.endpoint';

@Injectable()
export class RewardPointRuleApiService extends RewardPointRuleApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  /**
   * Calculate reward point rule by ODAC
   * @param payload - The calculation request data
   * @param configs - Optional HTTP client configuration
   * @returns Promise containing the calculation response
   */
  async calculateRewardPointRule(
    payload: RewardPointRuleCalculatorByOdacRequest[],
    configs?: HttpClientRequestConfig,
  ): Promise<RewardPointRuleCalculatorByOdacResponse[]> {
    const axiosRes = await this.httpClientService.post(
      RewardPointRuleApiEndpoint.CALCULATE_REWARD_POINT_RULE,
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * Calculate reward point rule by quantity
   * @param payload - The calculation request data
   * @param configs - Optional HTTP client configuration
   * @returns Promise containing the calculation response
   */
  async calculateRewardPointRuleByQuantity(
    payload: RewardPointRuleCalculatorByQuantityRequest[],
    configs?: HttpClientRequestConfig,
  ): Promise<RewardPointRuleCalculatorByQuantityResponseItem[]> {
    const axiosRes = await this.httpClientService.post(
      RewardPointRuleApiEndpoint.CALCULATE_REWARD_POINT_RULE_BY_QUANTITY,
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }
}
