import { HttpClientRequestConfig } from '../../http-client/src';
import {
  RewardPointRuleCalculatorByOdacRequest,
  RewardPointRuleCalculatorByOdacResponse,
  RewardPointRuleCalculatorByQuantityRequest,
  RewardPointRuleCalculatorByQuantityResponseItem,
} from './dto';

export abstract class RewardPointRuleApiAbstract {
  abstract calculateRewardPointRule(
    payload: RewardPointRuleCalculatorByOdacRequest[],
    configs?: HttpClientRequestConfig,
  ): Promise<RewardPointRuleCalculatorByOdacResponse[]>;

  abstract calculateRewardPointRuleByQuantity(
    payload: RewardPointRuleCalculatorByQuantityRequest[],
    configs?: HttpClientRequestConfig,
  ): Promise<RewardPointRuleCalculatorByQuantityResponseItem[]>;
}
