import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

export class RewardAllEnumsItemDto<T = number | string> {
  @Expose()
  @ApiProperty()
  value: T;

  @Expose()
  @ApiProperty()
  description: string;

  @Expose()
  @ApiProperty()
  type: 'number' | 'string';
}

export class RewardAllEnumsResponseDto {
  @Expose()
  @ApiProperty({ type: RewardAllEnumsItemDto, isArray: true })
  @Type(() => RewardAllEnumsItemDto)
  applyRoles: RewardAllEnumsItemDto<number>[];

  @Expose()
  @ApiProperty({ type: RewardAllEnumsItemDto, isArray: true })
  @Type(() => RewardAllEnumsItemDto)
  orderProperties: RewardAllEnumsItemDto<number>[];

  @Expose()
  @ApiProperty({ type: RewardAllEnumsItemDto, isArray: true })
  @Type(() => RewardAllEnumsItemDto)
  orderAttributes: RewardAllEnumsItemDto<number>[];

  @Expose()
  @ApiProperty({ type: RewardAllEnumsItemDto, isArray: true })
  @Type(() => RewardAllEnumsItemDto)
  rewardRuleProperties: RewardAllEnumsItemDto<number>[];

  @Expose()
  @ApiProperty({ type: RewardAllEnumsItemDto, isArray: true })
  @Type(() => RewardAllEnumsItemDto)
  rewardTypes: RewardAllEnumsItemDto<number>[];

  @Expose()
  @ApiProperty({ type: RewardAllEnumsItemDto, isArray: true })
  @Type(() => RewardAllEnumsItemDto)
  status: RewardAllEnumsItemDto<number>[];

  @Expose()
  @ApiProperty({ type: RewardAllEnumsItemDto, isArray: true })
  @Type(() => RewardAllEnumsItemDto)
  transactionTypes: RewardAllEnumsItemDto<number>[];

  @Expose()
  @ApiProperty({ type: RewardAllEnumsItemDto, isArray: true })
  @Type(() => RewardAllEnumsItemDto)
  orderChannels: RewardAllEnumsItemDto<number>[];

  @Expose()
  @ApiProperty({ type: RewardAllEnumsItemDto, isArray: true })
  @Type(() => RewardAllEnumsItemDto)
  orderSteps: RewardAllEnumsItemDto<number>[];

  @Expose()
  @ApiProperty({ type: RewardAllEnumsItemDto, isArray: true })
  @Type(() => RewardAllEnumsItemDto)
  rewardRuleTypes: RewardAllEnumsItemDto<string>[];

  @Expose()
  @ApiProperty({ type: RewardAllEnumsItemDto, isArray: true })
  @Type(() => RewardAllEnumsItemDto)
  rewardRuleSchemes: RewardAllEnumsItemDto<string>[];
}
