import { UpdateTypeMessageDto, GetMessageDto, MessageRes, ListCustomerResponseDto, CustomerParamDto } from './dto';
import { HttpClientRequestConfig } from '../../http-client/src';

export abstract class CarApiAbstract {
  // Define abstract methods for broadcast message operations
  abstract updateMessageInterestType(
    payload: UpdateTypeMessageDto[],
    configs?: HttpClientRequestConfig,
  ): Promise<boolean>;
  abstract getMessage(payload: GetMessageDto, configs?: HttpClientRequestConfig): Promise<MessageRes>;

  abstract createCustomerTag(
    body: CustomerParamDto,
    configs?: HttpClientRequestConfig,
  ): Promise<ListCustomerResponseDto>;

  abstract getCustomerTag(
    params: CustomerParamDto,
    configs?: HttpClientRequestConfig,
  ): Promise<ListCustomerResponseDto>;
}
