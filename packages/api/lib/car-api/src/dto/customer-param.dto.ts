import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';

export class CustomerParamDto {
  @ApiProperty({
    type: String,
    example: 'lcvId',
    required: false,
  })
  @Expose()
  @IsOptional()
  vaccinationCode?: string;

  @ApiProperty({
    type: String,
    example: '6e44b8cf-679e-4797-bdd5-ec86d1d6bccb',
    required: false,
  })
  @IsOptional()
  @Expose()
  tagId?: string;

  @ApiProperty({
    type: String,
    example: '<EMAIL>',
    required: false,
  })
  @Expose()
  @IsOptional()
  email?: string;

  @ApiProperty({
    type: String,
    example: '123456789',
    required: false,
  })
  @Expose()
  @IsOptional()
  phoneNumber?: string;
}
