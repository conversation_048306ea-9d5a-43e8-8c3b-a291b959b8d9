import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class ListCustomerResponseDto {
  @ApiProperty({
    type: String,
    example: '6e44b8cf-679e-4797-bdd5-ec86d1d6bccb',
    required: false,
  })
  @Expose()
  phoneNumber: string;

  @ApiProperty({
    type: String,
    example: '6e44b8cf-679e-4797-bdd5-ec86d1d6bccb',
    required: false,
  })
  @Expose()
  email: string;

  @ApiProperty({
    type: String,
    example: '6e44b8cf-679e-4797-bdd5-ec86d1d6bccb',
    required: false,
  })
  @Expose()
  vaccinationCode: string;

  @ApiProperty({
    type: String,
    example: '6e44b8cf-679e-4797-bdd5-ec86d1d6bccb',
    required: false,
  })
  @Expose()
  tagId: string;

  @ApiProperty({
    type: String,
    example: '6e44b8cf-679e-4797-bdd5-ec86d1d6bccb',
    required: false,
  })
  @Expose()
  code: string;

  @ApiProperty({
    type: String,
    example: '6e44b8cf-679e-4797-bdd5-ec86d1d6bccb',
    required: false,
  })
  @Expose()
  name: string;

  @ApiProperty({
    type: String,
    example: '6e44b8cf-679e-4797-bdd5-ec86d1d6bccb',
    required: false,
  })
  @Expose()
  extraProperties: any;
}
