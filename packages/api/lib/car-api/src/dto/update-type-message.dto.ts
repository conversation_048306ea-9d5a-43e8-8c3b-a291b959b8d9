import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

export class UpdateTypeMessageDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  brcHistoryId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  employeeCode?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  employeeName?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  callInNumber?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  brcPhoneNumber?: string;
}
