import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import { IsArray, IsNumber, IsOptional, IsPositive, IsString, Min, ValidateNested } from 'class-validator';

export class GetMessageDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Expose()
  keyword?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Expose()
  fromDate?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Expose()
  toDate?: string;

  @ApiProperty({ required: false, type: [Number] })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Expose()
  type?: number[];

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @IsPositive()
  @Min(1)
  @Expose()
  pageSize?: number = 10;

  @ApiProperty({ required: false })
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @IsPositive()
  @Expose()
  page?: number = 1;
}

export class DetailMessageDto {
  @ApiProperty()
  @IsString()
  @Expose()
  id: string;

  @ApiProperty()
  @IsString()
  @Expose()
  brcHistoryId: string;

  @ApiProperty()
  @IsString()
  @Expose()
  employeeCode: string;

  @ApiProperty()
  @IsString()
  @Expose()
  employeeName: string;

  @ApiProperty()
  @IsString()
  @Expose()
  callInNumber: string;

  @ApiProperty()
  @IsString()
  @Expose()
  brcPhoneNumber: string;

  @ApiProperty()
  @IsString()
  @Expose()
  createdDate: string;

  @ApiProperty()
  @IsString()
  @Expose()
  modifiedDate: string;
}

export class ItemMessageDto {
  @ApiProperty()
  @IsString()
  @Expose()
  id: string;

  @ApiProperty()
  @IsString()
  @Expose()
  idref: string;

  @ApiProperty()
  @IsNumber()
  @Expose()
  sourceId: number;

  @ApiProperty()
  @IsString()
  @Expose()
  senderId: string;

  @ApiProperty()
  @IsString()
  @Expose()
  customerId: string;

  @ApiProperty()
  @IsString()
  @Expose()
  phoneNumber: string;

  @ApiProperty()
  @IsString()
  @Expose()
  content: string;

  @ApiProperty()
  @IsString()
  @Expose()
  campaignId: string;

  @ApiProperty()
  @IsString()
  @Expose()
  campaignName: string;

  @ApiProperty()
  @IsString()
  @Expose()
  sentDate: string;

  @ApiProperty()
  @IsString()
  @Expose()
  type: string;

  @ApiProperty()
  @IsNumber()
  @Expose()
  status: number;

  @ApiProperty()
  @IsString()
  @Expose()
  createdDate: string;

  @ApiProperty()
  @IsString()
  @Expose()
  createdBy: string;

  @ApiProperty()
  @IsString()
  @Expose()
  modifiedDate: string;

  @ApiProperty()
  @IsString()
  @Expose()
  modifiedBy: string;

  @ApiProperty()
  @IsNumber()
  @Expose()
  followupCount: number;

  @ApiProperty({ type: [DetailMessageDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DetailMessageDto)
  @Expose()
  details: DetailMessageDto[];
}

export class MessageRes {
  @ApiProperty({ type: [ItemMessageDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ItemMessageDto)
  @Expose()
  items: ItemMessageDto[];

  @ApiProperty()
  @IsNumber()
  @Expose()
  totalCount: number;
}
