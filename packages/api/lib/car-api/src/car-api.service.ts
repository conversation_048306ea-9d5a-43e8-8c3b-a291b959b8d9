import { Injectable } from '@nestjs/common';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import { _interpolateString, _checkExceptionFromAxiosRes } from '../../functions';
import { CarApiAbstract } from './car-api.abstract';
import { CarApiEndpoint } from './car-api.endpoint';
import { UpdateTypeMessageDto, GetMessageDto, MessageRes, CustomerParamDto, ListCustomerResponseDto } from './dto';

@Injectable()
export class CarApiService extends CarApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  async updateMessageInterestType(
    payload: UpdateTypeMessageDto[],
    configs?: HttpClientRequestConfig,
  ): Promise<boolean> {
    const axiosRes = await this.httpClientService.post(
      _interpolateString(CarApiEndpoint.UPDATE_MESSAGE_INTEREST_TYPE),
      payload,
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async getMessage(payload: GetMessageDto, configs?: HttpClientRequestConfig): Promise<MessageRes> {
    const axiosRes = await this.httpClientService.post(
      _interpolateString(CarApiEndpoint.GET_MESSAGE),
      payload,
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async getCustomerTag(params: CustomerParamDto, configs?: HttpClientRequestConfig): Promise<ListCustomerResponseDto> {
    const axiosRes = await this.httpClientService.get(CarApiEndpoint.CUSTOMER_TAGS, {
      ...configs,
      params,
    });

    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes.data;
  }
  async createCustomerTag(body: CustomerParamDto, configs?: HttpClientRequestConfig): Promise<ListCustomerResponseDto> {
    const axiosRes = await this.httpClientService.post(
      CarApiEndpoint.CUSTOMER_TAGS,
      { ...body },
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes.data;
  }
}
