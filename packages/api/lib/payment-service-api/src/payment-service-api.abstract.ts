import { HttpClientRequestConfig } from '../../http-client/src';
import {
  ApprovePayAwaitingDto,
  CancelCollectByStatusRequestDto,
  CancelPayByStatusDto,
  CreatePayDto,
  CreateCollectDto,
  CreateCollectResponseDto,
  FinishCollectByStatusRequestDto,
  FinishPayByStatusResponseDto,
  GetCollectCategoriesDetailRes,
  GetCollectCategoriesRes,
  GetPayCategoriesByShopDetailQueryDto,
  GetPayCategoriesByShopDetailResponseDto,
  GetPayCategoriesByShopQueryDto,
  GetRevenueListDto,
  PayCategoryDto,
  PayResponseDto,
  UpdateCollectDto,
  UpdatePayDto,
} from './dto';
import { GetRevenueDetailDto, GetRevenueDetailRes } from './dto/get-revenue-detail.dto';

export abstract class PaymentServiceApiAbstract {
  abstract getRevenueList(configs?: HttpClientRequestConfig): Promise<GetRevenueListDto>;

  abstract getRevenueDetail(
    payload: GetRevenueDetailDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetRevenueDetailRes>;

  // ------------------- PHIẾU THU(COLLECT)  -------------------

  abstract getCollectCategories(configs?: HttpClientRequestConfig): Promise<GetCollectCategoriesRes[]>;

  abstract getCollectCategoriesDetail(
    categoryCode: string,
    configs?: HttpClientRequestConfig,
  ): Promise<GetCollectCategoriesDetailRes>;

  abstract createCollect(body: CreateCollectDto): Promise<CreateCollectResponseDto>;

  /**
   * Cập nhật phiếu thu (Collect)
   */
  abstract updateCollect(
    orexCode: string,
    request: UpdateCollectDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CreateCollectResponseDto>;

  /**
   * Hủy phiếu thu theo trạng thái
   */
  abstract cancelCollectByStatus(
    orexCode: string,
    request: CancelCollectByStatusRequestDto,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean>;

  /**
   * Hoàn phiếu thu
   */
  abstract finishCollectByStatus(
    orexCode: string,
    request: FinishCollectByStatusRequestDto,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean>;

  // ------------------- PHIẾU CHI(PAY)  -------------------

  /**
   * Lấy danh sách hạng mục phiếu chi
   */
  abstract getPayCategoriesByShop(
    query: GetPayCategoriesByShopQueryDto,
    configs?: HttpClientRequestConfig,
  ): Promise<PayCategoryDto[]>;

  /**
   * Lấy chi tiết hạng mục phiếu chi
   */
  abstract getPayCategoryByShopDetail(
    request: GetPayCategoriesByShopDetailQueryDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetPayCategoriesByShopDetailResponseDto>;

  /**
   * Tạo phiếu chi
   */
  abstract createPay(body: CreatePayDto, configs?: HttpClientRequestConfig): Promise<PayResponseDto>;

  /**
   * Cập nhật phiếu chi
   */
  abstract updatePay(body: UpdatePayDto, configs?: HttpClientRequestConfig): Promise<PayResponseDto>;

  /**
   * Cập nhật trạng thái chưa duyệt phiếu chi
   */
  abstract approvePayAwaiting(body: ApprovePayAwaitingDto, configs?: HttpClientRequestConfig): Promise<boolean>;

  /**
   * Huỷ phiếu chi
   */
  abstract cancelPayByStatus(body: CancelPayByStatusDto, configs?: HttpClientRequestConfig): Promise<boolean>;

  /**
   * Hoàn phiếu chi
   */
  abstract finishPayByStatus(configs?: HttpClientRequestConfig): Promise<FinishPayByStatusResponseDto>;
}
