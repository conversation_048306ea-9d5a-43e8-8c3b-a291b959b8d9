import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';

export class GetRevenueListDto {
  @ApiProperty()
  @Expose()
  @IsOptional()
  shopCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  orexCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  orexType?: number;

  @ApiProperty({ type: [Number] })
  @Expose()
  @IsOptional()
  status?: number[];

  @ApiProperty()
  @Expose()
  @IsOptional()
  fromDate?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  toDate?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  categoryCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  pageNumber?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  pageSize?: number;
}
