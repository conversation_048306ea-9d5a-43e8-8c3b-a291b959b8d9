import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class GetRevenueDetailDto {
  @ApiProperty()
  @Expose()
  @IsOptional()
  orexCode?: string;
}

class RevenueDetailImage {
  @ApiProperty()
  @Expose()
  @IsOptional()
  image?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  status?: number;
}

export class GetRevenueDetailRes {
  @ApiProperty()
  @Expose()
  @IsOptional()
  orexCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  orexType?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  shopCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  categoryCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  categoryName?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  limitAmount?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  limitAmountRemain?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  quantity?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  unitCode?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  unitName?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  price?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  totalAmount?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  description?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  explanation?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  status?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  approvalDate?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  approvedBy?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  approvedName?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  reason?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  paymentCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  createdDate?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  createdName?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  modifiedDate?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  modifiedBy?: string;

  @ApiProperty({ type: [RevenueDetailImage] })
  @Expose()
  @IsOptional()
  @Type(() => RevenueDetailImage)
  images?: RevenueDetailImage[];
}
