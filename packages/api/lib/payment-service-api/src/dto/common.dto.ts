import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

export class RevenueImage {
  @ApiProperty()
  @Expose()
  @IsOptional()
  image: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  status: number;
}

export class Collect {
  @ApiProperty()
  @Expose()
  @IsOptional()
  shopCode: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  categoryCode: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  quantity: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  unitName: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  price: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  totalAmount: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  description: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  explanation: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  createdBy: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  createdName: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @Type(() => Image)
  images: RevenueImage[];
}

export class Pay {
  @ApiProperty()
  @Expose()
  @IsOptional()
  shopCode: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  categoryCode: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  limitAmount: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  limitAmountRemain: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  quantity: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  unitName: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  price: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  totalAmount: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  description: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  explanation: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  createdBy: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  createdName: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @Type(() => Image)
  images: RevenueImage[];
}

export class ModifiedInfoDto {
  @ApiProperty()
  @Expose()
  @IsString()
  modifiedBy: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedName: string;
}
