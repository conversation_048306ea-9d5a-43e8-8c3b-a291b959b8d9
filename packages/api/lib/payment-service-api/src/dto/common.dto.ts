import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsOptional, IsString, IsNumber, IsDate, IsArray, ValidateNested } from 'class-validator';

// ================= BASE IMAGE DTO =================
export class BaseImageDto {
  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  image: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  status: number;
}

// Alias for backward compatibility
export class RevenueImage extends BaseImageDto {}

// ================= BASE TRANSACTION FIELDS =================
export class BaseTransactionDto {
  @ApiProperty()
  @Expose()
  @IsString()
  shopCode: string;

  @ApiProperty()
  @Expose()
  @IsString()
  categoryCode: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @Expose()
  @IsString()
  unitName: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  price: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  totalAmount: number;

  @ApiProperty()
  @Expose()
  @IsString()
  description: string;

  @ApiProperty()
  @Expose()
  @IsString()
  explanation: string;

  @ApiProperty()
  @Expose()
  @IsString()
  createdBy: string;

  @ApiProperty()
  @Expose()
  @IsString()
  createdName: string;

  @ApiProperty({ type: [BaseImageDto] })
  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BaseImageDto)
  images: BaseImageDto[];
}

// ================= BASE RESPONSE FIELDS =================
export class BaseOrexResponseDto {
  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  orexCode: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  orexType: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  categoryName: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  unitCode: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  status: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsDate()
  approvalDate: Date;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  approvedBy: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  approvedName: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  reason: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  paymentCode: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsDate()
  createdDate: Date;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsDate()
  modifiedDate: Date;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  modifiedBy: string;
}

// ================= LEGACY CLASSES (for backward compatibility) =================
export class Collect extends BaseTransactionDto {
  // Inherits all fields from BaseTransactionDto
}

export class Pay extends BaseTransactionDto {
  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  limitAmount: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  limitAmountRemain: number;
}

// ================= SPECIALIZED DTOs =================
export class PaySpecificFieldsDto {
  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmount: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmountRemain: number;
}

export class FinishTransactionFieldsDto {
  @ApiProperty()
  @Expose()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  price: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  totalAmount: number;

  @ApiProperty()
  @Expose()
  @IsString()
  description: string;

  @ApiProperty()
  @Expose()
  @IsString()
  explanation: string;

  @ApiProperty({ type: [BaseImageDto] })
  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BaseImageDto)
  images: BaseImageDto[];
}

export class ModifiedInfoDto {
  @ApiProperty()
  @Expose()
  @IsString()
  modifiedBy: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedName: string;
}
