import { ApiProperty } from '@nestjs/swagger';
import { ModifiedInfoDto, RevenueImage } from '../common.dto';

export class FinishPayByStatusResponseDto extends ModifiedInfoDto {
  @ApiProperty()
  quantity: number;

  @ApiProperty()
  price: number;

  @ApiProperty()
  totalAmount: number;

  @ApiProperty()
  limitAmountRemain: number;

  @ApiProperty()
  description: string;

  @ApiProperty()
  explanation: string;

  @ApiProperty({ type: [RevenueImage] })
  images: RevenueImage[];
}
