// get-pay-categories-by-shop.query.dto.ts
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

export class GetPayCategoriesByShopQueryDto {
  @ApiPropertyOptional()
  @Expose()
  @IsOptional()
  @IsString()
  shopCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  provinceCode: string;

  @ApiProperty()
  @Expose()
  @IsString()
  districtCode: string;
}

export class PayCategoryDto {
  @ApiProperty()
  @Expose()
  categoryCode: string;

  @ApiProperty()
  @Expose()
  categoryName: string;

  @ApiProperty()
  @Expose()
  description: string;
}
