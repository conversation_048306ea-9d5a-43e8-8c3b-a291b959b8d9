import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsNumber, IsString, ValidateNested } from 'class-validator';
import { ModifiedInfoDto, RevenueImage } from '../common.dto';

export class ApprovePayAwaitingDto extends ModifiedInfoDto {
  @ApiProperty()
  @Expose()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  price: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  totalAmount: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmountRemain: number;

  @ApiProperty()
  @Expose()
  @IsString()
  description: string;

  @ApiProperty()
  @Expose()
  @IsString()
  explanation: string;

  @ApiProperty({ type: [RevenueImage] })
  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RevenueImage)
  images: RevenueImage[];
}
