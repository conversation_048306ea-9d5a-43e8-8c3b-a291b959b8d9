import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';

export class GetPayCategoriesByShopDetailQueryDto {
  @ApiProperty({ required: true })
  @Expose()
  @IsString()
  categoryCode: string;

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  @IsString()
  shopCode?: string;

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  @IsNumber()
  provinceCode?: number;

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  @IsNumber()
  districtCode?: number;

  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  @IsNumber()
  revenue?: number;
}

class LimitDefineDto {
  @ApiProperty()
  @Expose()
  @IsString()
  categoryCode: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  unitCode: number;

  @ApiProperty()
  @Expose()
  @IsString()
  unitName: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmountForMonth: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmountRemainForMonth: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  priceDefault: number;

  @ApiProperty()
  @Expose()
  @IsString()
  accountBravo: string;

  @ApiProperty()
  @Expose()
  @IsBoolean()
  isUploadImage: boolean;

  @ApiProperty()
  @Expose()
  @IsNumber()
  revenueTo: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  revenueFrom: number;

  @ApiProperty()
  @Expose()
  @IsString()
  shopCode: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  districtCode: number;

  @ApiProperty()
  @Expose()
  @IsString()
  districtName: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  provinceCode: number;

  @ApiProperty()
  @Expose()
  @IsString()
  provinceName: string;

  @ApiProperty()
  @Expose()
  @IsString()
  note: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  status: number;
}

export class GetPayCategoriesByShopDetailResponseDto {
  @ApiProperty()
  @Expose()
  @IsString()
  categoryCode: string;

  @ApiProperty()
  @Expose()
  @IsString()
  categoryName: string;

  @ApiProperty()
  @Expose()
  @IsString()
  description: string;

  @ApiProperty({ type: LimitDefineDto })
  @Expose()
  @Type(() => LimitDefineDto)
  limitDefine: LimitDefineDto;
}
