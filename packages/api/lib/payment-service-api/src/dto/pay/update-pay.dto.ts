import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { RevenueImage } from '../common.dto';

export class UpdatePayDto {
  @ApiProperty()
  @Expose()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  price: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  totalAmount: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmountRemain: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  explanation?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedBy: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedName: string;

  @ApiProperty({ type: [RevenueImage] })
  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RevenueImage)
  images: RevenueImage[];
}
