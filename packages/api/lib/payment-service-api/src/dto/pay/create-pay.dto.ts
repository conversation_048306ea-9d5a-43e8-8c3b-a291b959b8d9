import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsDate, IsNumber, IsString, ValidateNested } from 'class-validator';
import { RevenueImage } from '../common.dto';

export class CreatePayDto {
  @ApiProperty()
  @Expose()
  @IsString()
  shopCode: string;

  @ApiProperty()
  @Expose()
  @IsString()
  categoryCode: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmount: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmountRemain: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @Expose()
  @IsString()
  unitName: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  price: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  totalAmount: number;

  @ApiProperty()
  @Expose()
  @IsString()
  description: string;

  @ApiProperty()
  @Expose()
  @IsString()
  explanation: string;

  @ApiProperty()
  @Expose()
  @IsString()
  createdBy: string;

  @ApiProperty()
  @Expose()
  @IsString()
  createdName: string;

  @ApiProperty({ type: [RevenueImage] })
  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RevenueImage)
  images: RevenueImage[];
}
export class PayResponseDto {
  @ApiProperty()
  @Expose()
  @IsString()
  orexCode: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  orexType: number;

  @ApiProperty()
  @Expose()
  @IsString()
  shopCode: string;

  @ApiProperty()
  @Expose()
  @IsString()
  categoryCode: string;

  @ApiProperty()
  @Expose()
  @IsString()
  categoryName: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmount: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmountRemain: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  unitCode: number;

  @ApiProperty()
  @Expose()
  @IsString()
  unitName: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  price: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  totalAmount: number;

  @ApiProperty()
  @Expose()
  @IsString()
  description: string;

  @ApiProperty()
  @Expose()
  @IsString()
  explanation: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  status: number;

  @ApiProperty()
  @Expose()
  @IsDate()
  approvalDate: Date;

  @ApiProperty()
  @Expose()
  @IsString()
  approvedBy: string;

  @ApiProperty()
  @Expose()
  @IsString()
  approvedName: string;

  @ApiProperty()
  @Expose()
  @IsString()
  reason: string;

  @ApiProperty()
  @Expose()
  @IsString()
  paymentCode: string;

  @ApiProperty()
  @Expose()
  @IsDate()
  createdDate: Date;

  @ApiProperty()
  @Expose()
  @IsString()
  createdName: string;

  @ApiProperty()
  @Expose()
  @IsDate()
  modifiedDate: Date;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedBy: string;

  @ApiProperty({ type: [RevenueImage] })
  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RevenueImage)
  images: RevenueImage[];
}
