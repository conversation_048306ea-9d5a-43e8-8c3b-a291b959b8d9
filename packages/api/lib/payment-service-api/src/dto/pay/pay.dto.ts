import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsDate, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { BaseImageDto, BaseTransactionDto, ModifiedInfoDto } from '../common.dto';

// ================= PAY REQUEST DTOs =================
export class CreatePayDto extends BaseTransactionDto {
  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmount: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmountRemain: number;
}

export class UpdatePayDto {
  @ApiProperty()
  @Expose()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  price: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  totalAmount: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmountRemain: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  explanation?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedBy: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedName: string;

  @ApiProperty({ type: [BaseImageDto] })
  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BaseImageDto)
  images: BaseImageDto[];
}

// ================= PAY RESPONSE DTOs =================
export class PayResponseDto extends BaseTransactionDto {
  @ApiProperty()
  @Expose()
  @IsString()
  orexCode: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  orexType: number;

  @ApiProperty()
  @Expose()
  @IsString()
  categoryName: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmount: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmountRemain: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  unitCode: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  status: number;

  @ApiProperty()
  @Expose()
  @IsDate()
  approvalDate: Date;

  @ApiProperty()
  @Expose()
  @IsString()
  approvedBy: string;

  @ApiProperty()
  @Expose()
  @IsString()
  approvedName: string;

  @ApiProperty()
  @Expose()
  @IsString()
  reason: string;

  @ApiProperty()
  @Expose()
  @IsString()
  paymentCode: string;

  @ApiProperty()
  @Expose()
  @IsDate()
  createdDate: Date;

  @ApiProperty()
  @Expose()
  @IsDate()
  modifiedDate: Date;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedBy: string;
}

export class FinishPayByStatusResponseDto extends ModifiedInfoDto {
  @ApiProperty()
  quantity: number;

  @ApiProperty()
  price: number;

  @ApiProperty()
  totalAmount: number;

  @ApiProperty()
  limitAmountRemain: number;

  @ApiProperty()
  description: string;

  @ApiProperty()
  explanation: string;

  @ApiProperty({ type: [BaseImageDto] })
  images: BaseImageDto[];
}

// ================= PAY SPECIFIC DTOs =================
export class ApprovePayAwaitingDto {
  @ApiProperty()
  @Expose()
  @IsString()
  approvedBy: string;

  @ApiProperty()
  @Expose()
  @IsString()
  approvedName: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  reason?: string;
}

export class CancelPayByStatusDto {
  @ApiProperty()
  @Expose()
  @IsString()
  reason: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedBy: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedName: string;
}
