// Common DTOs
export * from './common.dto';

// New consolidated DTOs
export * from './collect';
export * from './pay/pay.dto';

// Legacy DTOs that don't conflict (for backward compatibility)
export * from './collect/get-collect-categories-detail.dto';
export * from './collect/get-collect-categories.dto';

// Specific legacy DTOs from pay folder (non-conflicting)
export * from './pay/get-pay-categories-by-shop.dto';
export * from './pay/get-pay-category-detail-response.dto';

// Revenue DTOs
export * from './get-revenue-detail.dto';
export * from './get-revenue-list.dto';

// Legacy image DTOs (aliases for backward compatibility)
export { BaseImageDto as CollectImageDto, BaseImageDto as FinishCollectImageDto } from './common.dto';
