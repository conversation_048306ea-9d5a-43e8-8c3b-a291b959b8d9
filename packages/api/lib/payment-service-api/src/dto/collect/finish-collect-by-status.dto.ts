// finish-collect-by-status.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsNumber, IsString, ValidateNested, IsArray } from 'class-validator';
import { ModifiedInfoDto } from '../common.dto';

export class FinishCollectImageDto {
  @ApiProperty()
  @Expose()
  @IsString()
  image: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  status: number;
}

export class FinishCollectByStatusRequestDto extends ModifiedInfoDto {
  @ApiProperty()
  @Expose()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  price: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  totalAmount: number;

  @ApiProperty()
  @Expose()
  @IsString()
  description: string;

  @ApiProperty()
  @Expose()
  @IsString()
  explanation: string;

  @ApiProperty({ type: [FinishCollectImageDto] })
  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FinishCollectImageDto)
  images: FinishCollectImageDto[];
}
