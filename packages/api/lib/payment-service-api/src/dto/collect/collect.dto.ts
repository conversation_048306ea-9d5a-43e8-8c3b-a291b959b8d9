import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsDate, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';
import { BaseImageDto, BaseTransactionDto, ModifiedInfoDto } from '../common.dto';

// ================= COLLECT REQUEST DTOs =================
export class CreateCollectDto extends BaseTransactionDto {
  // Inherits all fields from BaseTransactionDto
  // No additional fields needed for collect creation
}

export class UpdateCollectDto {
  @ApiProperty()
  @Expose()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  price: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  totalAmount: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  explanation?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedBy: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedName: string;

  @ApiProperty({ type: [BaseImageDto] })
  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BaseImageDto)
  images: BaseImageDto[];
}

// ================= COLLECT RESPONSE DTOs =================
export class CollectResponseDto extends BaseTransactionDto {
  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  orexCode: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  orexType: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  categoryName: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  limitAmount: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  limitAmountRemain: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  unitCode: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  status: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsDate()
  approvalDate: Date;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  approvedBy: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  approvedName: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  reason: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  paymentCode: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsDate()
  createdDate: Date;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsDate()
  modifiedDate: Date;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  modifiedBy: string;
}

// Alias for backward compatibility
export class CreateCollectResponseDto extends CollectResponseDto {}

// ================= COLLECT SPECIFIC DTOs =================
export class FinishCollectByStatusRequestDto extends ModifiedInfoDto {
  @ApiProperty()
  @Expose()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  price: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  totalAmount: number;

  @ApiProperty()
  @Expose()
  @IsString()
  description: string;

  @ApiProperty()
  @Expose()
  @IsString()
  explanation: string;

  @ApiProperty({ type: [BaseImageDto] })
  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BaseImageDto)
  images: BaseImageDto[];
}

export class CancelCollectByStatusRequestDto {
  @ApiProperty()
  @Expose()
  @IsString()
  reason: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedBy: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedName: string;
}

// ================= COLLECT CATEGORIES DTOs =================
export class GetCollectCategoriesDto {
  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  shopCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  categoryCode?: string;
}

export class CollectCategoryDto {
  @ApiProperty()
  @Expose()
  @IsString()
  categoryCode: string;

  @ApiProperty()
  @Expose()
  @IsString()
  categoryName: string;

  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmount: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  limitAmountRemain: number;

  @ApiProperty()
  @Expose()
  @IsNumber()
  status: number;
}

export class GetCollectCategoriesDetailDto {
  @ApiProperty()
  @Expose()
  @IsString()
  shopCode: string;

  @ApiProperty()
  @Expose()
  @IsString()
  categoryCode: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  page?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  size?: number;
}
