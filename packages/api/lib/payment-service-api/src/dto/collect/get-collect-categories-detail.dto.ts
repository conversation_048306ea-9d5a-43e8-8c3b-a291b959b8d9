import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';

export class GetCollectCategoriesDetailRes {
  @ApiProperty()
  @Expose()
  @IsOptional()
  id?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  categoryCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  categoryName?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  description?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  status?: number;
}
