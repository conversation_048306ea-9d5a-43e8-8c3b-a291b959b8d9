import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsString, IsNumber, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class CollectImageDto {
  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  image: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  status: number;
}

// ------------------- REQUEST DTO -------------------
export class CreateCollectDto {
  @ApiProperty()
  @IsString()
  shopCode: string;

  @ApiProperty()
  @IsString()
  categoryCode: string;

  @ApiProperty()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @IsString()
  unitName: string;

  @ApiProperty()
  @IsNumber()
  price: number;

  @ApiProperty()
  @IsNumber()
  totalAmount: number;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsString()
  explanation: string;

  @ApiProperty()
  @IsString()
  createdBy: string;

  @ApiProperty()
  @IsString()
  createdName: string;

  @ApiProperty({ type: [CollectImageDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CollectImageDto)
  images: CollectImageDto[];
}

// ------------------- RESPONSE DTO -------------------
export class CreateCollectResponseDto {
  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  orexCode: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  orexType: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  shopCode: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  categoryCode: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  categoryName: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  limitAmount: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  limitAmountRemain: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  unitCode: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  unitName: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  price: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  totalAmount: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  description: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  explanation: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  status: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  approvalDate: Date;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  approvedBy: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  approvedName: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  reason: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  paymentCode: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  createdDate: Date;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  createdName: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  modifiedDate: Date;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  modifiedBy: string;

  @ApiProperty({ type: [CollectImageDto] })
  @Expose()
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CollectImageDto)
  images: CollectImageDto[];
}
