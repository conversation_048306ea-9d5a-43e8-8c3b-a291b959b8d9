import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString, IsArray, ValidateNested, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { CollectImageDto } from './collect/create-collect.dto';

export class UpdateCollectDto {
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  quantity: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  price: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  totalAmount: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  description: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  explanation: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  modifiedBy: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  modifiedName: string;

  @ApiProperty({ type: [CollectImageDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CollectImageDto)
  images: CollectImageDto[];
}
