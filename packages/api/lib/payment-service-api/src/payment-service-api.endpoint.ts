export enum PaymentServiceApiEndpoint {
  // Inventory
  GET_INVENTORY_QUANTITY = 'api/InventoryTransit/get-inventory-quantity',

  // Revenue list
  GET_REVENUE_LIST = '/api/orex/histories',
  GET_REVENUE_DETAIL_BY_OREXCODE = '/api/orex/{orexCode}',

  // Revenue - Collect (Phiếu thu)
  GET_COLLECT_CATEGORIES = '/api/orex/categories/collect',
  GET_COLLECT_CATEGORIES_DETAIL = '/api/orex/categories/collect/{categoryCode}/detail',
  CREATE_COLLECT = '/api/orex/collect',
  UPDATE_COLLECT = '/api/orex/collect/{orexCode}',
  CANCEL_COLLECT_BY_STATUS = '/api/orex/collect/{orexCode}/cancel',
  FINISH_COLLECT_BY_STATUS = '/api/orex/collect/{orexCode}/finish',

  // Revenue - Pay (Phiếu chi)
  GET_PAY_CATEGORIES_BY_SHOP = '/api/orex/categories/pay',
  GET_PAY_CATEGORIES_BY_SHOP_DETAIL = '/api/orex/categories/pay/{categoryCode}/detail',
  CREATE_PAY = '/api/orex/pay',
  UPDATE_PAY = '/api/orex/pay/{orexCode}',
  APPROVE_PAY_AWAITING = '/api/orex/pay/{orexCode}/awaiting-approval',
  CANCEL_PAY_BY_STATUS = '/api/orex/pay/{orexCode}/cancel',
  FINISH_PAY_BY_STATUS = '/api/orex/pay/{orexCode}/finish',
}
