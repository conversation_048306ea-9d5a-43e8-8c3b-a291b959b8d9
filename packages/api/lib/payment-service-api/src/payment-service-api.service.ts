import { Injectable } from '@nestjs/common';
import { _checkExceptionFromAxiosRes, _transformPlainToInstance } from '../../functions';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import {
  ApprovePayAwaitingDto,
  CancelCollectByStatusRequestDto,
  CancelPayByStatusDto,
  CreatePayDto,
  CreateCollectDto,
  CreateCollectResponseDto,
  FinishCollectByStatusRequestDto,
  FinishPayByStatusResponseDto,
  GetCollectCategoriesDetailRes,
  GetCollectCategoriesRes,
  GetPayCategoriesByShopDetailQueryDto,
  GetPayCategoriesByShopDetailResponseDto,
  GetPayCategoriesByShopQueryDto,
  GetRevenueListDto,
  PayCategoryDto,
  PayResponseDto,
  UpdatePayDto,
} from './dto';
import { GetRevenueDetailDto, GetRevenueDetailRes } from './dto/get-revenue-detail.dto';
import { PaymentServiceApiAbstract } from './payment-service-api.abstract';
import { PaymentServiceApiEndpoint } from './payment-service-api.endpoint';

@Injectable()
export class PaymentServiceApiService extends PaymentServiceApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  async getRevenueList(configs?: HttpClientRequestConfig): Promise<GetRevenueListDto> {
    const axiosRes = await this.httpClientService.post(PaymentServiceApiEndpoint.GET_REVENUE_LIST, configs);
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(GetRevenueListDto, axiosRes?.data) as GetRevenueListDto;
  }

  async getRevenueDetail(
    payload: GetRevenueDetailDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetRevenueDetailRes> {
    const axiosRes = await this.httpClientService.get(
      PaymentServiceApiEndpoint.GET_REVENUE_DETAIL_BY_OREXCODE.replace('{orexCode}', payload.orexCode),
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(GetRevenueDetailRes, axiosRes?.data) as GetRevenueDetailRes;
  }

  /**
   * Lấy danh sách danh mục phiếu thu
   */
  async getCollectCategories(configs?: HttpClientRequestConfig): Promise<GetCollectCategoriesRes[]> {
    const axiosRes = await this.httpClientService.get(PaymentServiceApiEndpoint.GET_COLLECT_CATEGORIES, configs);
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(GetCollectCategoriesRes, axiosRes?.data) as GetCollectCategoriesRes[];
  }

  /**
   * Lấy chi tiết danh mục phiếu thu
   */
  async getCollectCategoriesDetail(
    categoryCode: string,
    configs?: HttpClientRequestConfig,
  ): Promise<GetCollectCategoriesDetailRes> {
    const axiosRes = await this.httpClientService.get(
      PaymentServiceApiEndpoint.GET_COLLECT_CATEGORIES_DETAIL.replace('{categoryCode}', categoryCode),
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(GetCollectCategoriesDetailRes, axiosRes?.data) as GetCollectCategoriesDetailRes;
  }

  // ------------------- PHIẾU THU(COLLECT)  -------------------

  /**
   * Tạo phiếu thu
   */
  async createCollect(request: CreateCollectDto, configs?: HttpClientRequestConfig): Promise<CreateCollectResponseDto> {
    const axiosRes = await this.httpClientService.post(PaymentServiceApiEndpoint.CREATE_COLLECT, request, {
      ...configs,
    });

    _checkExceptionFromAxiosRes(axiosRes);

    return _transformPlainToInstance(CreateCollectResponseDto, axiosRes?.data || null) as CreateCollectResponseDto;
  }

  /**
   * Cập nhật phiếu thu (Collect)
   */
  async updateCollect(
    orexCode: string,
    request: UpdateCollectDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CreateCollectResponseDto> {
    const axiosRes = await this.httpClientService.put(
      PaymentServiceApiEndpoint.UPDATE_COLLECT.replace('{orexCode}', orexCode),
      request,
      { ...configs },
    );

    _checkExceptionFromAxiosRes(axiosRes);

    return _transformPlainToInstance(CreateCollectResponseDto, axiosRes?.data || null) as CreateCollectResponseDto;
  }

  async cancelCollectByStatus(
    orexCode: string,
    request: CancelCollectByStatusRequestDto,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean> {
    const endpoint = PaymentServiceApiEndpoint.CANCEL_COLLECT_BY_STATUS.replace('{orexCode}', orexCode);

    const axiosRes = await this.httpClientService.post(endpoint, request, { ...configs });

    _checkExceptionFromAxiosRes(axiosRes);

    return axiosRes?.data ?? false;
  }

  async finishCollectByStatus(
    orexCode: string,
    request: FinishCollectByStatusRequestDto,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean> {
    const endpoint = PaymentServiceApiEndpoint.FINISH_COLLECT_BY_STATUS.replace('{orexCode}', orexCode);

    const axiosRes = await this.httpClientService.post(endpoint, request, { ...configs });

    _checkExceptionFromAxiosRes(axiosRes);

    return axiosRes?.data ?? false;
  }

  // ------------------- PHIẾU CHI(PAY)  -------------------

  async getPayCategoriesByShop(
    query: GetPayCategoriesByShopQueryDto,
    configs?: HttpClientRequestConfig,
  ): Promise<PayCategoryDto[]> {
    const endpoint = PaymentServiceApiEndpoint.GET_PAY_CATEGORIES_BY_SHOP;

    const axiosRes = await this.httpClientService.get(endpoint, {
      ...configs,
      params: query,
    });

    _checkExceptionFromAxiosRes(axiosRes);

    return axiosRes?.data ?? [];
  }

  async getPayCategoryByShopDetail(
    request: GetPayCategoriesByShopDetailQueryDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetPayCategoriesByShopDetailResponseDto> {
    const endpoint = PaymentServiceApiEndpoint.GET_PAY_CATEGORIES_BY_SHOP_DETAIL;

    const axiosRes = await this.httpClientService.get(endpoint, {
      ...configs,
      params: request,
    });

    _checkExceptionFromAxiosRes(axiosRes);

    return axiosRes?.data ?? null;
  }

  async createPay(request: CreatePayDto, configs?: HttpClientRequestConfig): Promise<PayResponseDto> {
    const axiosRes = await this.httpClientService.post(PaymentServiceApiEndpoint.CREATE_PAY, request, {
      ...configs,
    });

    _checkExceptionFromAxiosRes(axiosRes);

    return _transformPlainToInstance(PayResponseDto, axiosRes?.data || null) as PayResponseDto;
  }

  async updatePay(request: UpdatePayDto, configs?: HttpClientRequestConfig): Promise<PayResponseDto> {
    const axiosRes = await this.httpClientService.put(PaymentServiceApiEndpoint.UPDATE_PAY, request, {
      ...configs,
    });

    _checkExceptionFromAxiosRes(axiosRes);

    return _transformPlainToInstance(PayResponseDto, axiosRes?.data || null) as PayResponseDto;
  }

  async approvePayAwaiting(request: ApprovePayAwaitingDto, configs?: HttpClientRequestConfig): Promise<boolean> {
    const axiosRes = await this.httpClientService.post(PaymentServiceApiEndpoint.APPROVE_PAY_AWAITING, request, {
      ...configs,
    });

    _checkExceptionFromAxiosRes(axiosRes);

    return axiosRes?.data === true;
  }

  async cancelPayByStatus(request: CancelPayByStatusDto, configs?: HttpClientRequestConfig): Promise<boolean> {
    const axiosRes = await this.httpClientService.post(PaymentServiceApiEndpoint.CANCEL_PAY_BY_STATUS, request, {
      ...configs,
    });

    _checkExceptionFromAxiosRes(axiosRes);

    return Boolean(axiosRes?.data);
  }

  async finishPayByStatus(configs?: HttpClientRequestConfig): Promise<FinishPayByStatusResponseDto> {
    const axiosRes = await this.httpClientService.post(PaymentServiceApiEndpoint.FINISH_PAY_BY_STATUS, {
      ...configs,
    });

    _checkExceptionFromAxiosRes(axiosRes);

    return _transformPlainToInstance(
      FinishPayByStatusResponseDto,
      axiosRes?.data || null,
    ) as FinishPayByStatusResponseDto;
  }
}
