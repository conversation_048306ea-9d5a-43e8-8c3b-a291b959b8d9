import {
  CreatePayDto,
  CreateCollectDto,
  UpdatePayDto,
  UpdateCollectDto,
  PayResponseDto,
  CollectResponseDto,
  BaseImageDto,
  // Legacy aliases (for backward compatibility)
  CollectImageDto,
  FinishCollectImageDto,
} from '../src/dto';

/**
 * Example: <PERSON><PERSON>ch sử dụng các DTO mới sau khi refactor
 */

// ================= CREATING PAY TRANSACTION =================
export function createPayExample(): CreatePayDto {
  const payRequest: CreatePayDto = {
    // Fields từ BaseTransactionDto
    shopCode: 'SHOP001',
    categoryCode: 'OFFICE_SUPPLIES',
    quantity: 10,
    unitName: 'boxes',
    price: 50000,
    totalAmount: 500000,
    description: 'Mu<PERSON> văn phòng phẩm cho chi nhánh',
    explanation: 'Cần thiết cho hoạt động hàng ngày',
    createdBy: 'user123',
    createdName: 'Nguyễn Văn A',
    
    // Pay-specific fields
    limitAmount: 1000000,
    limitAmountRemain: 500000,
    
    // Images sử dụng BaseImageDto
    images: [
      {
        image: 'base64-encoded-receipt-image',
        status: 1
      },
      {
        image: 'base64-encoded-invoice-image', 
        status: 1
      }
    ]
  };

  return payRequest;
}

// ================= CREATING COLLECT TRANSACTION =================
export function createCollectExample(): CreateCollectDto {
  const collectRequest: CreateCollectDto = {
    // Fields từ BaseTransactionDto (giống như Pay)
    shopCode: 'SHOP002',
    categoryCode: 'VACCINE_FEES',
    quantity: 5,
    unitName: 'doses',
    price: 200000,
    totalAmount: 1000000,
    description: 'Thu phí tiêm vaccine COVID-19',
    explanation: 'Dịch vụ tiêm vaccine cho khách hàng',
    createdBy: 'nurse456',
    createdName: 'Trần Thị B',
    
    // Collect không có limitAmount như Pay
    images: [
      {
        image: 'base64-encoded-payment-proof',
        status: 1
      }
    ]
  };

  return collectRequest;
}

// ================= UPDATING TRANSACTIONS =================
export function updatePayExample(): UpdatePayDto {
  const updateRequest: UpdatePayDto = {
    quantity: 12, // Tăng số lượng
    price: 45000, // Giảm giá
    totalAmount: 540000, // Tổng tiền mới
    limitAmountRemain: 460000, // Số tiền còn lại
    description: 'Cập nhật: Mua văn phòng phẩm với giá tốt hơn',
    explanation: 'Đã thương lượng được giá tốt hơn',
    modifiedBy: 'manager789',
    modifiedName: 'Lê Văn C',
    images: [
      {
        image: 'base64-encoded-updated-receipt',
        status: 1
      }
    ]
  };

  return updateRequest;
}

export function updateCollectExample(): UpdateCollectDto {
  const updateRequest: UpdateCollectDto = {
    quantity: 7, // Tăng số lượng
    price: 180000, // Giảm giá
    totalAmount: 1260000, // Tổng tiền mới
    description: 'Cập nhật: Thêm dịch vụ tư vấn',
    explanation: 'Bao gồm cả dịch vụ tư vấn sức khỏe',
    modifiedBy: 'supervisor999',
    modifiedName: 'Phạm Thị D',
    images: [
      {
        image: 'base64-encoded-updated-payment-proof',
        status: 1
      }
    ]
  };

  return updateRequest;
}

// ================= WORKING WITH RESPONSES =================
export function handlePayResponse(response: PayResponseDto): void {
  console.log('Pay transaction created:', {
    orexCode: response.orexCode,
    orexType: response.orexType,
    shopCode: response.shopCode,
    categoryName: response.categoryName,
    totalAmount: response.totalAmount,
    status: response.status,
    paymentCode: response.paymentCode,
    createdDate: response.createdDate
  });
}

export function handleCollectResponse(response: CollectResponseDto): void {
  console.log('Collect transaction created:', {
    orexCode: response.orexCode,
    orexType: response.orexType,
    shopCode: response.shopCode,
    categoryName: response.categoryName,
    totalAmount: response.totalAmount,
    status: response.status,
    paymentCode: response.paymentCode,
    createdDate: response.createdDate
  });
}

// ================= BACKWARD COMPATIBILITY EXAMPLES =================
export function legacyImageUsage(): void {
  // Sử dụng alias cho backward compatibility
  const legacyImage: CollectImageDto = {
    image: 'legacy-image-data',
    status: 1
  };

  const finishImage: FinishCollectImageDto = {
    image: 'finish-image-data',
    status: 1
  };

  // Cả hai đều thực chất là BaseImageDto
  console.log('Legacy images still work:', { legacyImage, finishImage });
}

// ================= UTILITY FUNCTIONS =================
export function createImageDto(imageBase64: string, status: number = 1): BaseImageDto {
  return {
    image: imageBase64,
    status
  };
}

export function validateTransactionAmount(
  quantity: number, 
  price: number, 
  expectedTotal: number
): boolean {
  return quantity * price === expectedTotal;
}

// ================= USAGE IN SERVICE =================
export class PaymentServiceExample {
  async createPayment(request: CreatePayDto): Promise<PayResponseDto> {
    // Validate request
    if (!validateTransactionAmount(request.quantity, request.price, request.totalAmount)) {
      throw new Error('Invalid transaction amount calculation');
    }

    // Process payment...
    const response: PayResponseDto = {
      ...request,
      orexCode: 'PAY-' + Date.now(),
      orexType: 1,
      categoryName: 'Office Supplies',
      unitCode: 1,
      status: 1,
      approvalDate: new Date(),
      approvedBy: 'system',
      approvedName: 'Auto Approved',
      reason: 'Automatic approval',
      paymentCode: 'PMT-' + Date.now(),
      createdDate: new Date(),
      modifiedDate: new Date(),
      modifiedBy: request.createdBy
    };

    return response;
  }

  async createCollection(request: CreateCollectDto): Promise<CollectResponseDto> {
    // Similar processing for collect...
    const response: CollectResponseDto = {
      ...request,
      orexCode: 'COL-' + Date.now(),
      orexType: 2,
      categoryName: 'Vaccine Fees',
      limitAmount: 0, // Collect không có limit
      limitAmountRemain: 0,
      unitCode: 2,
      status: 1,
      approvalDate: new Date(),
      approvedBy: 'system',
      approvedName: 'Auto Approved',
      reason: 'Automatic approval',
      paymentCode: 'COL-' + Date.now(),
      createdDate: new Date(),
      modifiedDate: new Date(),
      modifiedBy: request.createdBy
    };

    return response;
  }
}
