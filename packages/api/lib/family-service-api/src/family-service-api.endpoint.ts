export enum FamilyServiceApiEndpoint {
  UPDATE_PERSON_BY_LCVID = 'api/person/update-person-info',
  GET_FAMILY_BY_LCVID = 'api/family/get-by-lcvid',
  GET_FAMILY_BY_LCVID_V2 = 'api/family/get-by-lcvid-v2',
  GET_LIST_PRIMARY_PERSON = 'api/person/get-primary-person',
  GET_LIST_PRIMARY_PERSON_BY_PHONE = 'api/person/get-primary-person-by-phone',
  GET_BY_CORE_CUST_ID = 'api/person/get-by-core-cust-Id',
  GET_PERSON_CONTACT_BY_CUST_ID = 'api/contact/get-db-by-coreCusId',
  CREATE_PERSON = 'api/person',
  GET_CUSTOMER_BY_PHONE = 'api/person/get-by-phone',
  SEARCH_PERSON = 'api/person/search',
  GET_PERSON_BY_LCV_ID = 'api/person/lcvid/{0}',
  GET_PERSON_BY_LCV_ID_V2 = 'api/person/lcvid-v2/{0}',
  GET_BY_NATIONAL_CODE = 'api/person/national-vac-code/{0}',
  PERSON_UPDATE_NATIONAL_VACCINE_CODE = 'api/person/update-nationalVaccineCode-by-lcvId',
  VERIFY_PHONE = 'api/person/verify-by-phone',
  GET_MANY_BY_LCV_ID = 'api/person/get-many-by-lcvId',
  GET_MANY_BY_LCV_ID_V2 = 'api/person/get-many-by-lcvId-v2',
  UPDATE_PERSON = 'api/person',
  SEARCH_PRIMARY_PERSON_EXACTLY = 'api/person/search-primary-person-exactly',
  GET_PERSON_BY_ID = 'api/person/{0}',
  UPDATE_PHONE_NUMBER = 'api/person/update-phone-number',
  DELETE_PHONE_NUMBER = 'api/person/delete-phone-number',
  ACTIVE_PERSON = 'api/person/active-person',
  GET_PERSON_AND_HOT_BY_LCVID = 'api/person/get-person-and-host-by-lcvId',
  SEARCH_PRIMARY_PERSON_BY_KEY_WORD = 'api/person/search-primary-person-by-keyword',
  SEARCH_PERSON_FAMILY = 'api/person/search-family',
  GET_PERSON_BY_CUST_ID = 'api/person/get-by-core-cust-Id/{0}',
  PERSON_UPDATE_ADDRESS = 'api/person/update-person-address-by-lcvId',
  UPDATE_PERSON_V2 = 'api/person/update-person-info',
  ADD_FAMILY_MEMBER = 'api/person/add-single-person',
  UPDATE_FAMILY_MEMBER = 'api/person/update-family-member-detail',
  SEARCH_FAMILY_BY_KEY_WORD = 'api/person/search-family-by-key-word',
  CHANGE_FAMILY_PROFILE = 'api/person/change-family-profile',
  GET_LIST_TITLE = 'api/title/get-list',
  DELETE_FAMILY_PROFILE = 'api/person/remove-family-profile',
  INACTIVE_PERSON_CONTACT_BY_PERSON_ID = 'api/contact/inactive-personContact-by-personId',
  CHANGE_HOST_FOR_PERSON = 'api/person/change-host-person',
  GET_PRIMARI_SHORT_PERSON = 'api/contact/get-primary-short-person',
}
