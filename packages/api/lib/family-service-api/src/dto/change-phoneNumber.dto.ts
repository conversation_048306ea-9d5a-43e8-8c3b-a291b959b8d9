import { ApiProperty, PickType } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { Person } from './person.dto';

export class VerifyPhoneNumberDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  personId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  newPhoneNumber: string;
}

export class VerifyPhoneNumberRes {
  @ApiProperty()
  @Expose()
  @IsOptional()
  message?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @Type(() => Person)
  personInfo?: Person;

  @ApiProperty()
  @Expose()
  @IsOptional()
  isAbleForUpdate?: boolean;
}

export class UpdatePhoneNumberDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  personId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  oldPhoneNumber: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  newPhoneNumber: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  modifiedByStaffName: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  modifiedByStaffCode: string;

  @ApiProperty()
  @IsNotEmpty()
  @Expose()
  verifyMethod: number;
}

export class DeletePhoneNumberDto extends PickType(UpdatePhoneNumberDto, [
  'modifiedByStaffCode',
  'modifiedByStaffName',
  'personId',
  'verifyMethod',
]) {}

export class PersonInfors {
  @ApiProperty()
  @IsOptional()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  lcvId?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  name?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  nationalVaccineCode?: string;
}
export class CheckDupPhoneNumberRes {
  @ApiProperty()
  @IsOptional()
  @Expose()
  result?: boolean;

  @ApiProperty({ isArray: true, type: PersonInfors })
  @IsOptional()
  @Expose()
  personInfors?: PersonInfors[];
}
