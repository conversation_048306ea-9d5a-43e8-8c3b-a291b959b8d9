import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { AgeFormat, Person } from './person.dto';

export class QuotaItemsDto {
  @ApiProperty()
  @Expose()
  @IsOptional()
  id?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  quotaId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  sku?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  skuName?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  status?: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  createdDate?: Date;
}

export class QuotaRespone {
  @ApiProperty()
  @IsOptional()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  companyId?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  emailDomain?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  categoriesId?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  categoriesName?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  productTypeId?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  productTypeName?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  branchId?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  branchName?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  groupId?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  groupName?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  quantity?: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  fromDate?: Date;

  @ApiProperty()
  @IsOptional()
  @Expose()
  toDate?: Date;

  @ApiProperty()
  @IsOptional()
  @Expose()
  isActive?: boolean;

  @ApiProperty()
  @IsOptional()
  @Expose()
  isApplyAllEmail?: boolean;

  @ApiProperty({ isArray: true, type: QuotaItemsDto })
  @IsOptional()
  @Expose()
  quotaItems?: QuotaItemsDto[];
}

export class GetRemainingQuotaRes {
  @ApiProperty()
  @IsOptional()
  @Expose()
  remainingQuota?: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  quotaRespone?: QuotaRespone;
}

export class GetPersonByIdRes extends Person {
  @ApiProperty()
  @IsOptional()
  @Expose()
  isFpter?: boolean;

  @ApiProperty({ isArray: true, type: GetRemainingQuotaRes })
  @IsOptional()
  @Expose()
  quotas?: GetRemainingQuotaRes[];

  @ApiProperty()
  @IsOptional()
  @Expose()
  customerAge?: AgeFormat;
}

export class PayloadGetManyLcvIdDto {
  @ApiProperty({ isArray: true, type: String })
  @Expose()
  @IsOptional()
  lcvId?: string[];
}

export class GetPersonByIdV2Res extends Person {
  @ApiProperty()
  @Expose()
  @IsOptional()
  lcvIdDisplayName?: string;
}

export class GetDetailFamilyDto {
  @ApiProperty({ required: false })
  @Expose()
  @IsOptional()
  familyId?: string;
}
