import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

export class ItemPersonContactDto {
  @ApiProperty()
  @IsString()
  @Expose()
  id: string;

  @ApiProperty()
  @IsString()
  @Expose()
  personId: string;

  @ApiProperty()
  @IsString()
  @Expose()
  contactId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  contactName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  contactPhone?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  titleId?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  titleName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  nickName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  avatarSuggest?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  avatarUpload?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  lcvId?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  familyProfileId?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  name?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  gender?: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  dateOfBirth?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  phoneNumber?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  nationalVaccineCode?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  creationTime?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  createdBy?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  modifiedTime?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  modifiedBy?: string;
}

export class ResItemDto {
  @ApiProperty({ isArray: true, type: ItemPersonContactDto })
  @IsOptional()
  @Expose()
  @Type(() => ItemPersonContactDto)
  personContacts?: ItemPersonContactDto[];
}
export class ResPersonContactDto {
  @ApiProperty()
  @IsOptional()
  @Expose()
  totalCount?: number;

  @ApiProperty({ isArray: true, type: ResItemDto })
  @IsOptional()
  @Expose()
  @Type(() => ResItemDto)
  items?: ResItemDto[];
}

export class ReqPersonContactDto {
  @ApiProperty()
  @IsOptional()
  @Expose()
  coreCusId?: string;
}
