import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { Person } from './person.dto';
import { IsOptional } from 'class-validator';

export class PersonByCustIdRes extends Person {
  @ApiProperty()
  @IsOptional()
  @Expose()
  lcvIdDisplayName?: string;
}

export class ListPersonByCustIdRes {
  @ApiProperty({ isArray: true, type: PersonByCustIdRes })
  @Expose()
  items: Array<PersonByCustIdRes>;
}
