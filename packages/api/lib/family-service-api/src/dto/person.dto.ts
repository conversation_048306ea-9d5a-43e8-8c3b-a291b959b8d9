import { ApiProperty, OmitType } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

export class AgeFormat {
  @ApiProperty()
  @IsOptional()
  @Expose()
  year?: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  month?: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  week?: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  day?: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  now?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  birth?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  textDisplay?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  remainingDaysInMonths?: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  remainingDaysInWeeks?: number;
}

export class FamilyProfileDetails {
  @ApiProperty()
  @IsOptional()
  @Expose()
  name?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  phoneNumber?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  gender?: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  source?: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  titleId?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  titleName?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  familyProfileId?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  personId?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  dateOfBirth: Date;

  @ApiProperty()
  @IsOptional()
  @Expose()
  lcvId: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  customerId?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  nationalVaccineCode?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  nationalVaccineId?: string;

  @ApiProperty()
  @Expose()
  @IsBoolean()
  @IsOptional()
  isHost?: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  from?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  to?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  ageUnitCode?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  ageUnit?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  isAdding?: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  isGuardian?: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  type?: boolean;

  @ApiProperty({ type: AgeFormat })
  @IsOptional()
  @Expose()
  customerAge?: AgeFormat;
}

export class HistoryTgqg {
  @ApiProperty()
  @IsOptional()
  @Expose()
  vacxinId?: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  tenVacxin?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  phongBenh?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  mui?: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  trangThai?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  ngayTiem?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  diaDiem?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  coSoCapNhat?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  lichSuTiemId?: string;
}

export class FamilyProfileDetailsTCQG {
  @ApiProperty()
  @Expose()
  @IsOptional()
  name?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  dateOfBirth?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  type?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  isGuardian?: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  identityCard?: string;
}

export class ListVaccineHistoryDto {
  @ApiProperty()
  @IsOptional()
  @Expose()
  sku?: number; // vaccine Id

  @ApiProperty()
  @IsOptional()
  @Expose()
  vaccineName?: string; // tên vaccine

  @ApiProperty()
  @IsOptional()
  @Expose()
  taxonomies?: string; // phòng bệnh

  @ApiProperty()
  @IsOptional()
  @Expose()
  injection?: number; // mũi thứ

  @ApiProperty()
  @IsOptional()
  @Expose()
  status?: string; // trạng thái

  @ApiProperty()
  @IsOptional()
  @Expose()
  vaccineDate?: string; // ngày tiêm

  @ApiProperty()
  @IsOptional()
  @Expose()
  location?: string; // địa điểm

  @ApiProperty()
  @IsOptional()
  @Expose()
  facilityUpdate?: string; // cơ sở cập nhật

  @ApiProperty()
  @IsOptional()
  @Expose()
  vaccineHistoryId?: string; // lịch sử tiêm Id
}

export class Pregnancy {
  @ApiProperty()
  @Expose()
  @IsOptional()
  id?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  personId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  pregnancyNumber?: number;

  @ApiProperty({ type: Date })
  @Expose()
  @IsOptional()
  pregnancyDate?: string | Date;

  @ApiProperty()
  @Expose()
  @IsOptional()
  status?: number;

  @ApiProperty({ type: Date })
  @Expose()
  @IsOptional()
  estimateDate?: string | Date;

  @ApiProperty()
  @Expose()
  @IsOptional()
  isDeleted?: boolean;

  @ApiProperty({ type: Date })
  @Expose()
  @IsOptional()
  creationTime?: string | Date;

  @ApiProperty()
  @Expose()
  @IsOptional()
  createdBy?: string;

  @ApiProperty({ type: Date })
  @Expose()
  @IsOptional()
  modifiedTime?: string | Date;

  @ApiProperty()
  @Expose()
  @IsOptional()
  modifiedBy?: string;

  @ApiProperty({ description: 'Kiểm tra thông tin mang thai của khách hàng nếu > 10 tháng là true < 10 là false' })
  @Expose()
  @IsOptional()
  isValid?: boolean;
}

export class PersonAddress {
  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  personId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  lcvId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  provinceCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  provinceName?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  districtCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  districtName?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  wardCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  wardName?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  type?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsBoolean()
  status?: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsBoolean()
  isNew?: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  TCQGProvinceCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  TCQGDistrictCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  TCQGWardCode?: string;
}

export class Person {
  @ApiProperty()
  @Expose()
  lcvId: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  nationalVaccineCode: string;

  @ApiProperty()
  @Expose()
  nationalVaccineId: string;

  @ApiProperty()
  @Expose()
  identityCard: string;

  @ApiProperty()
  @Expose()
  dateOfBirth?: Date;

  @ApiProperty()
  @Expose()
  gender: number;

  @ApiProperty()
  @Expose()
  phoneNumber: string;

  @ApiProperty()
  @Expose()
  jobTitle: string;

  @ApiProperty()
  @Expose()
  email: string;

  @ApiProperty()
  @Expose()
  frequentlyProvinceCode: string;

  @ApiProperty()
  @Expose()
  frequentlyProvinceName: string;

  @ApiProperty()
  @Expose()
  frequentlyDistrictCode: string;

  @ApiProperty()
  @Expose()
  frequentlyDistrictName: string;

  @ApiProperty()
  @Expose()
  frequentlyWardCode: string;

  @ApiProperty()
  @Expose()
  frequentlyWardName: string;

  @ApiProperty()
  @Expose()
  frequentlyAddress: string;

  @ApiProperty()
  @Expose()
  temporaryProvinceCode: string;

  @ApiProperty()
  @Expose()
  temporaryProvinceName: string;

  @ApiProperty()
  @Expose()
  temporaryDistrictCode: string;

  @ApiProperty()
  @Expose()
  temporaryDistrictName: string;

  @ApiProperty()
  @Expose()
  temporaryWardCode: string;

  @ApiProperty()
  @Expose()
  temporaryWardName: string;

  @ApiProperty()
  @Expose()
  temporaryAddress: string;

  @ApiProperty()
  @Expose()
  ethnicCode: string;

  @ApiProperty()
  @Expose()
  ethnicName: string;

  @ApiProperty()
  @Expose()
  isHost: boolean;

  @ApiProperty()
  @Expose()
  customerId: string;

  @ApiProperty()
  @Expose()
  guardianName: string;

  @ApiProperty()
  @Expose()
  guardianPhone: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  guardianDateOfBirth?: number;

  @ApiProperty()
  @Expose()
  avatarUrl: string;

  @ApiProperty()
  @Expose()
  isSameAddress: boolean;

  @ApiProperty()
  @Expose()
  isHaveGuardian: boolean;

  @ApiProperty()
  @Expose()
  note: string;

  @ApiProperty()
  @Expose()
  lastSyncTcqg: string;

  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty({ isArray: true, type: FamilyProfileDetails })
  @Type(() => FamilyProfileDetails)
  @Expose()
  @IsOptional()
  familyProfileDetails?: FamilyProfileDetails[];

  @ApiProperty()
  @IsOptional()
  @Expose()
  referralSourceId?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  referralSourceNote?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  familyProfileId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  historyTgqg?: HistoryTgqg[];

  @ApiProperty({ isArray: true, type: FamilyProfileDetailsTCQG })
  @Expose()
  @IsOptional()
  familyProfileDetailsTCQG?: FamilyProfileDetailsTCQG[];

  @ApiProperty({ isArray: true, type: ListVaccineHistoryDto })
  @Expose()
  @IsOptional()
  listVaccineHistory?: ListVaccineHistoryDto[];

  @ApiProperty()
  @Expose()
  from?: number;

  @ApiProperty()
  @Expose()
  to?: number;

  @ApiProperty()
  @Expose()
  ageUnitCode?: number;

  @ApiProperty()
  @Expose()
  ageUnit?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  nonPhoneCustomer?: boolean;

  @ApiProperty()
  @Expose()
  status?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  nationalityCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  nationalityName?: string;

  @ApiProperty({ type: AgeFormat })
  @IsOptional()
  @Expose()
  customerAge?: AgeFormat;

  @ApiProperty({ type: Pregnancy })
  @IsOptional()
  @Expose()
  @Type(() => Pregnancy)
  pregnancy?: Pregnancy[];

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  source?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({ isArray: true, type: PersonAddress })
  @Expose()
  @IsOptional()
  @Type(() => PersonAddress)
  personAddresses?: PersonAddress[];
}

export class FamilyProfileDetailsCreateDto extends OmitType(FamilyProfileDetails, [
  'familyProfileId',
  'personId',
] as const) {}

export class ReferralSourceDto {
  @ApiProperty()
  @IsOptional()
  @Expose()
  note?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  id?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  label?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  isActive?: boolean;
}

export class CreatePersonDto {
  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  source?: number;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  nationalVaccineCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  nationalVaccineId?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  identityCard?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  dateOfBirth?: Date;

  @ApiProperty()
  @Expose()
  @IsNumber()
  @IsOptional()
  gender?: number;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  @ValidateIf((obj) => obj.phoneNumber !== '')
  @Matches(/^(?!00000\d{5})\d{10,11}$/, {
    message: 'SĐT không hợp lệ, vui lòng kiểm tra lại',
  })
  phoneNumber?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  jobTitle?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  email?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  frequentlyProvinceCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @Expose()
  @IsOptional()
  frequentlyTCQGProvinceCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  frequentlyProvinceName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  frequentlyDistrictCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  frequentlyTCQGDistrictCode?: string;

  @ApiProperty({ description: 'Tỉnh người giám hộ' })
  @Expose()
  @IsString()
  @IsOptional()
  frequentlyDistrictName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  frequentlyWardCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  frequentlyTCQGWardCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  frequentlyWardName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  frequentlyAddress?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  temporaryProvinceCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  temporaryTCQGProvinceCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  temporaryProvinceName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  temporaryDistrictCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  temporaryTCQGDistrictCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  temporaryDistrictName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  temporaryWardCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  temporaryTCQGWardCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  temporaryWardName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  temporaryAddress?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  ethnicCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  ethnicName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  guardianName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  guardianPhone?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @Expose()
  @IsOptional()
  avatarUrl?: string;

  @ApiProperty()
  @Expose()
  @IsBoolean()
  @IsOptional()
  isSameAddress?: boolean;

  @ApiProperty()
  @Expose()
  @IsBoolean()
  @IsOptional()
  isHaveGuardian?: boolean;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  note?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  lastSyncTcqg?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  createdBy?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  modifiedBy?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  shopCode?: string;

  @ApiProperty({ isArray: true, type: FamilyProfileDetailsCreateDto })
  @Type(() => FamilyProfileDetailsCreateDto)
  @Expose()
  @ValidateNested({ each: true })
  @IsOptional()
  familyProfileDetails?: FamilyProfileDetailsCreateDto[];

  @ApiProperty()
  @Expose()
  @IsOptional()
  referralSource?: ReferralSourceDto;

  @ApiProperty()
  @Expose()
  @IsBoolean()
  @IsOptional()
  isHost?: boolean;

  @ApiProperty()
  @Expose()
  @IsBoolean()
  @IsOptional()
  nonPhoneCustomer?: boolean;

  @ApiProperty()
  @Expose()
  @IsBoolean()
  @IsOptional()
  sourceFromTCQG?: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  from?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  to?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsNumber()
  ageUnitCode?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  ageUnit?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  nationalityCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  nationalityName?: string;

  @ApiProperty({ isArray: true, type: PersonAddress })
  @Expose()
  @IsOptional()
  @Type(() => PersonAddress)
  personAddresses?: PersonAddress[];
}

export class CreatePersonRes {
  @ApiProperty()
  @Expose()
  personId: string;

  @ApiProperty()
  @Expose()
  customerId: string;

  @ApiProperty()
  @Expose()
  lcvId: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  nationalVaccineCode: string;

  @ApiProperty()
  @Expose()
  nationalVaccineId: string;

  @ApiProperty()
  @Expose()
  dateOfBirth: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  source?: number;

  @ApiProperty()
  @Expose()
  gender: number;

  @ApiProperty()
  @Expose()
  phoneNumber: string;

  @ApiProperty()
  @Expose()
  jobTitle: string;

  @ApiProperty()
  @Expose()
  email: string;

  @ApiProperty()
  @Expose()
  isHost: boolean;

  @ApiProperty()
  @Expose()
  avatarUrl: string;

  @ApiProperty()
  @Expose()
  note: string;

  @ApiProperty({ isArray: true, type: FamilyProfileDetails })
  @Type(() => FamilyProfileDetails)
  @Expose()
  @IsOptional()
  familyProfileDetails?: FamilyProfileDetails[];

  @ApiProperty()
  @Expose()
  @IsOptional()
  familyProfileId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  titleId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  titleName?: string;

  @ApiProperty()
  @Expose()
  from?: number;

  @ApiProperty()
  @Expose()
  to?: number;

  @ApiProperty()
  @Expose()
  ageUnitCode?: number;

  @ApiProperty()
  @Expose()
  ageUnit?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  nationalityCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  nationalityName?: string;

  @ApiProperty({ type: AgeFormat })
  @IsOptional()
  @Expose()
  customerAge?: AgeFormat;

  @ApiProperty({ type: Pregnancy })
  @IsOptional()
  @Expose()
  @Type(() => Pregnancy)
  pregnancy?: Pregnancy[];

  @ApiProperty({ isArray: true, type: PersonAddress })
  @Expose()
  @IsOptional()
  @Type(() => PersonAddress)
  personAddresses?: PersonAddress[];
}

export class VerifyByPhoneDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  phone: string;
}

export class VerifyByPhoneRes extends Person {}
