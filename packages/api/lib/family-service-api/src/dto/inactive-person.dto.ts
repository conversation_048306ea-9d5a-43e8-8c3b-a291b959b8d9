import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsBoolean, IsOptional, IsString } from 'class-validator';

export class InactivePersonDto {
  @ApiProperty()
  @Expose()
  @IsString()
  personId: string;

  @ApiProperty()
  @Expose()
  @IsBoolean()
  isActive: boolean;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  modifiedBy?: string;
}

export class DeletePersonContractDto {
  @ApiProperty()
  @Expose()
  @IsString()
  personId?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  @IsString()
  modifiedBy?: string;
}
