import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class ChangeHostForPersonDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  personId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  personIdHost: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  lcvIdPrimary?: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  modifiedBy?: string;
}
