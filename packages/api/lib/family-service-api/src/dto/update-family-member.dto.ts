import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

export class UpdateFamilyMemberDto {
  @ApiProperty()
  @IsString()
  @Expose()
  familyProfileId: string;

  @ApiProperty()
  @IsOptional()
  @Expose()
  personId?: string;

  @ApiProperty()
  @Expose()
  titleId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  phoneNumber?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  uniqkey?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  to?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  from?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  nonDateOfBirth?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  ageUnit?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  ageUnitCode?: number;
}
