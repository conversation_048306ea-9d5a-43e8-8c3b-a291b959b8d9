import { IsString } from 'class-validator';
import { Person } from './person.dto';
import { Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class GetPersonByNationalCodeRes extends Person {}

export class UpdateNationalVaccineCodeDto {
  @ApiProperty()
  @Expose()
  @IsString()
  lcvId: string;

  @ApiProperty()
  @Expose()
  @IsString()
  nationalVaccineCode: string;

  @ApiProperty()
  @Expose()
  @IsString()
  nationalVaccineId: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedBy: string;
}
