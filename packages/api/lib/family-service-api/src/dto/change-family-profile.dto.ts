import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { FamilyProfileDetails } from './person.dto';

export class ChangeFamilyProfileDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  personId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  oldFamilyId: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @Expose()
  newFamilyId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Expose()
  modifiedBy: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Expose()
  personIdReload: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Expose()
  hostLcvId?: string;
}

export class ChangeFamilyProfileResponseDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  status: number;

  @ApiProperty()
  @Expose()
  hostPersonId: string;

  @ApiProperty()
  @Expose()
  familyProfileDetails: FamilyProfileDetails[];
}
