import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

export class ReqUpdatePersonByLcvIdDto {
  @ApiProperty()
  @Expose()
  lcvId: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  name?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  email?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  dateOfBirth?: Date;

  @ApiProperty()
  @IsOptional()
  @Expose()
  gender?: number;

  @ApiProperty()
  @IsOptional()
  @Expose()
  modifiedBy?: string;
}
export class UpdateAddressDto {
  @ApiProperty()
  @Expose()
  @IsString()
  lcvId: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedBy: string;

  @ApiProperty()
  @Expose()
  @IsString()
  frequentlyProvinceCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  frequentlyProvinceName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  frequentlyDistrictCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  frequentlyDistrictName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  frequentlyWardCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  frequentlyWardName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  frequentlyAddress?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  temporaryProvinceCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  temporaryProvinceName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  temporaryDistrictCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  temporaryDistrictName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  temporaryWardCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  temporaryWardName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  temporaryAddress?: string;
}
