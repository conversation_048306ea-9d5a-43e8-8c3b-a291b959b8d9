import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

export class DeleteFamilyProfileDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  personIdNow?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  familyIdNow?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  personIdRemove?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  personIdHost?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  @Expose()
  modifiedBy?: string;
}
