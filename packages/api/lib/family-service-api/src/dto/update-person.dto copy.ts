import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { AgeFormat, CreatePersonDto, Person } from './person.dto';
import { GetRemainingQuotaRes } from './get-person-by-lcvid.dto';

export class UpdatePersonDto extends CreatePersonDto {
  @ApiProperty()
  @Expose()
  @IsNotEmpty()
  @IsString()
  id: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  familyProfileId?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  @IsOptional()
  lcvId?: string;

  @ApiProperty()
  @Expose()
  @IsBoolean()
  @IsOptional()
  isShouldNotCreateTcqg?: boolean;
}

export class UpdatePersonRes extends Person {
  @ApiProperty()
  @IsOptional()
  @Expose()
  isFpter?: boolean;

  @ApiProperty({ isArray: true, type: GetRemainingQuotaRes })
  @IsOptional()
  @Expose()
  quotas?: GetRemainingQuotaRes[];

  @ApiProperty()
  @IsOptional()
  @Expose()
  customerAge?: AgeFormat;
}

export class UpdateNationalVaccineCodeDto {
  @ApiProperty()
  @Expose()
  @IsString()
  lcvId: string;

  @ApiProperty()
  @Expose()
  @IsString()
  nationalVaccineCode: string;

  @ApiProperty()
  @Expose()
  @IsString()
  nationalVaccineId: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedBy: string;
}

export class UpdateAddressDto {
  @ApiProperty()
  @Expose()
  @IsString()
  lcvId: string;

  @ApiProperty()
  @Expose()
  @IsString()
  modifiedBy: string;

  @ApiProperty()
  @Expose()
  @IsString()
  frequentlyProvinceCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  frequentlyProvinceName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  frequentlyDistrictCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  frequentlyDistrictName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  frequentlyWardCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  frequentlyWardName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  frequentlyAddress?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  temporaryProvinceCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  temporaryProvinceName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  temporaryDistrictCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  temporaryDistrictName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  temporaryWardCode?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  temporaryWardName?: string;

  @ApiProperty()
  @Expose()
  @IsString()
  temporaryAddress?: string;
}
