import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';
import { FamilyProfileDetails } from './person.dto';

export class PersonContact {
  @ApiProperty()
  @Expose()
  personId?: string;

  @ApiProperty()
  @Expose()
  contactId?: string;

  @ApiProperty()
  @Expose()
  contactName?: string;

  @ApiProperty()
  @Expose()
  contactPhone?: string;

  @ApiProperty()
  @Expose()
  id?: string;
}

export class SearchFamilyByKeywordDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  @IsString()
  keyword?: string;
}

export class PersonIdentification {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  personId: string;

  @ApiProperty()
  @Expose()
  imageUrl: string;

  @ApiProperty()
  @Expose()
  modifiedBy: string;

  @ApiProperty()
  @Expose()
  identificationType: number;
}

export class SearchFamilyByKeywordResponseDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  lcvId: string;

  @ApiProperty()
  @Expose()
  nationalVaccineCode: string;

  @ApiProperty()
  @Expose()
  nationalVaccineId: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  gender: number;

  @ApiProperty()
  @Expose()
  dateOfBirth: Date;

  @ApiProperty()
  @Expose()
  phoneNumber: string;

  @ApiProperty()
  @Expose()
  frequentlyProvinceCode: string;

  @ApiProperty()
  @Expose()
  frequentlyProvinceName: string;

  @ApiProperty()
  @Expose()
  frequentlyDistrictCode: string;

  @ApiProperty()
  @Expose()
  frequentlyDistrictName: string;

  @ApiProperty()
  @Expose()
  frequentlyWardCode: string;

  @ApiProperty()
  @Expose()
  frequentlyWardName: string;

  @ApiProperty()
  @Expose()
  frequentlyAddress: string;

  @ApiProperty()
  @Expose()
  temporaryProvinceCode: string;

  @ApiProperty()
  @Expose()
  temporaryProvinceName: string;

  @ApiProperty()
  @Expose()
  temporaryDistrictCode: string;

  @ApiProperty()
  @Expose()
  temporaryDistrictName: string;

  @ApiProperty()
  @Expose()
  temporaryWardCode: string;

  @ApiProperty()
  @Expose()
  temporaryWardName: string;

  @ApiProperty()
  @Expose()
  temporaryAddress: string;

  @ApiProperty()
  @Expose()
  ethnicCode: string;

  @ApiProperty()
  @Expose()
  ethnicName: string;

  @ApiProperty()
  @Expose()
  guardianName: string;

  @ApiProperty()
  @Expose()
  note: string;

  @ApiProperty()
  @Expose()
  familyProfileId: string;

  @ApiProperty()
  @Expose()
  titleName: string;

  @ApiProperty()
  @Expose()
  isSearchHeader: boolean;

  @ApiProperty()
  @Expose()
  isHaveGuardian: boolean;

  @ApiProperty()
  @Expose()
  referralSourceId: string;

  @ApiProperty()
  @Expose()
  referralSourceNote: string;

  @ApiProperty()
  @Expose()
  nonPhoneCustomer: boolean;

  @ApiProperty()
  @Expose()
  source: number;

  @ApiProperty()
  @Expose()
  status: number;

  @ApiProperty()
  @Expose()
  nationalityCode: string;

  @ApiProperty()
  @Expose()
  nationalityName: string;

  @ApiProperty({ isArray: true, type: PersonContact })
  @Expose()
  @Type(() => PersonContact)
  personContacts?: PersonContact[];

  @ApiProperty({ isArray: true, type: PersonIdentification })
  @Expose()
  @Type(() => PersonIdentification)
  PersonIdentifications?: PersonIdentification[];

  @ApiProperty({ isArray: true, type: FamilyProfileDetails })
  @Expose()
  @Type(() => FamilyProfileDetails)
  familyProfileDetails?: FamilyProfileDetails[];
}
