import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class Member {
  @ApiProperty()
  @IsString()
  @Expose()
  name: string;

  @ApiProperty()
  @IsString()
  @Expose()
  phoneNumber: string;

  @ApiProperty()
  @IsNumber()
  @Expose()
  gender: number;

  @ApiProperty()
  @IsString()
  @Expose()
  titleId: string;

  @ApiProperty()
  @IsString()
  @Expose()
  titleName: string;

  @ApiProperty()
  @IsString()
  @Expose()
  lcvId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  uniqkey?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  to?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  from?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  nonDateOfBirth?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  ageUnit?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  ageUnitCode?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  source?: number;
}
export class AddFamilyMemberDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  lcvId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  shopCode?: string;

  @ApiProperty()
  @IsString()
  @Expose()
  createdBy: string;

  @ApiProperty()
  @Expose()
  member: Member;
}
