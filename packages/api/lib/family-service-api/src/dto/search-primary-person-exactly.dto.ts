import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional } from 'class-validator';

export class SearchPrimaryPersonExactlyDto {
  @ApiProperty()
  @Expose()
  @IsOptional()
  pageNumber: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  pageSize: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  name?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  phoneNumber?: number;

  @ApiProperty()
  @Expose()
  @IsOptional()
  dateOfBirthFrom?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  dateOfBirthTo?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  frequentlyProvinceCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  frequentlyDistrictCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  frequentlyWardCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  temporaryProvinceCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  temporaryDistrictCode?: string;

  @ApiProperty()
  @Expose()
  @IsOptional()
  temporaryWardCode?: string;
}
