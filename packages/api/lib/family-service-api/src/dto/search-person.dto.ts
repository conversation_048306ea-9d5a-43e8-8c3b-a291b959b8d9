import { ApiProperty, OmitType } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import { Person } from './person.dto';
import { IsOptional, IsString } from 'class-validator';

export class SearchPersonDto {
  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  @Type(() => Number)
  @Transform(({ value }) => value || 1)
  pageNumber?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  @Type(() => Number)
  @Transform(({ value }) => value || 10)
  pageSize?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  sorting?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  nationalVaccineCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  keyword?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  phoneNumber?: string;

  @ApiProperty({ required: false, description: '0. Name, 1. Nữ, 2. Khác' })
  @IsOptional()
  @Expose()
  @Type(() => Number)
  gender?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  dateOfBirthFrom?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  @IsString()
  dateOfBirthTo?: Date;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  provinceCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  districtCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  wardCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  ethnicCode?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Expose()
  isSearchNewAddress?: boolean;
}

export class SearchPersonRes {
  @ApiProperty()
  @Expose()
  totalCount: number;

  @ApiProperty({ isArray: true, type: Person })
  @Expose()
  items: Array<Person>;
}

export class SearchPersonFamilyRes extends OmitType(SearchPersonRes, ['items'] as const) {
  @ApiProperty({ isArray: true, type: Person })
  @Expose()
  items: Array<Person>;
}
