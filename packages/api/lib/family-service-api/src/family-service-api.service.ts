import { Injectable } from '@nestjs/common';
import { _checkExceptionFromAxiosRes, _interpolateString, _transformPlainToInstance } from '../../functions';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import {
  CreatePersonDto,
  CreatePersonRes,
  GetPersonByIdRes,
  GetPersonByIdV2Res,
  GetPersonByNationalCodeRes,
  PayloadGetManyLcvIdDto,
  Person,
  ReqGetFamilyByLcvIdDto,
  ReqUpdatePersonByLcvIdDto,
  SearchPersonDto,
  SearchPersonRes,
  UpdateNationalVaccineCodeDto,
  VerifyByPhoneDto,
  VerifyByPhoneRes,
  GetDetailFamilyDto,
  SearchPrimaryPersonExactlyDto,
  UpdatePhoneNumberDto,
  VerifyPhoneNumberRes,
  DeletePhoneNumberDto,
  InactivePersonDto,
  SearchPersonFamilyRes,
  ListPersonByCustIdRes,
  UpdateAddressDto,
  AddFamilyMemberDto,
  UpdateFamilyMemberDto,
  SearchFamilyByKeywordDto,
  SearchFamilyByKeywordResponseDto,
  ChangeFamilyProfileDto,
  ChangeFamilyProfileResponseDto,
  GetListTitleDto,
  DeleteFamilyProfileDto,
  DeletePersonContractDto,
  ChangeHostForPersonDto,
  GetPrimaryShortPersonDto,
} from './dto';
import { FamilyServiceApiAbstract } from './family-service-api.abstract';
import { FamilyServiceApiEndpoint } from './family-service-api.endpoint';
import { ReqPersonContactDto, ResPersonContactDto } from './dto/person-contact.dto';
import { UpdatePersonDto, UpdatePersonRes } from './dto/update-person.dto copy';

@Injectable()
export class FamilyServiceApiService extends FamilyServiceApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  /**
   * @description cập nhật thông tin person từ web vaccine
   * @param payload
   * @param configs
   * @returns
   */
  async updatePersonByLcvId(payload: ReqUpdatePersonByLcvIdDto, configs?: HttpClientRequestConfig): Promise<Person> {
    const axiosRes = await this.httpClientService.patch(
      _interpolateString(FamilyServiceApiEndpoint.UPDATE_PERSON_BY_LCVID),
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(Person, axiosRes?.data || null);
  }

  async getFamilyByLcvId(payload: ReqGetFamilyByLcvIdDto, configs?: HttpClientRequestConfig): Promise<Person> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(FamilyServiceApiEndpoint.GET_FAMILY_BY_LCVID),
      {
        ...configs,
        params: payload,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(Person, axiosRes?.data || null);
  }

  async getFamilyByLcvIdV2(payload: ReqGetFamilyByLcvIdDto, configs?: HttpClientRequestConfig): Promise<Person> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(FamilyServiceApiEndpoint.GET_FAMILY_BY_LCVID_V2),
      {
        ...configs,
        params: payload,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(Person, axiosRes?.data || null);
  }

  async getPrimaryPerson(payload: string[], configs?: HttpClientRequestConfig): Promise<GetPersonByIdRes[]> {
    const axiosRes = await this.httpClientService.post(
      _interpolateString(FamilyServiceApiEndpoint.GET_LIST_PRIMARY_PERSON),
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(Person, axiosRes?.data || null);
  }

  async getPrimaryPersonByPhone(phones: string[], configs?: HttpClientRequestConfig): Promise<Person[]> {
    const axiosRes = await this.httpClientService.post(
      _interpolateString(FamilyServiceApiEndpoint.GET_LIST_PRIMARY_PERSON_BY_PHONE),
      phones,
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data ?? null;
  }

  async getPrimaryPersonByCoreCustId(coreCustId: string, configs?: HttpClientRequestConfig): Promise<Person[]> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(`${FamilyServiceApiEndpoint.GET_BY_CORE_CUST_ID}/${coreCustId}`),
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(Person, axiosRes?.data?.items || null);
  }

  async getPersonContactByCoreCustId(
    payload: ReqPersonContactDto,
    configs?: HttpClientRequestConfig,
  ): Promise<ResPersonContactDto> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(`${FamilyServiceApiEndpoint.GET_PERSON_CONTACT_BY_CUST_ID}`),
      {
        ...configs,
        params: {
          ...payload,
        },
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async createPerson(payload: CreatePersonDto, configs?: HttpClientRequestConfig): Promise<CreatePersonRes> {
    const axiosRes = await this.httpClientService.post(
      _interpolateString(`${FamilyServiceApiEndpoint.CREATE_PERSON}`),
      payload,
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async getPersonByPhone(phoneNumber: string, configs?: HttpClientRequestConfig): Promise<Person[]> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(`${FamilyServiceApiEndpoint.GET_CUSTOMER_BY_PHONE}?phone=${phoneNumber}`),
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * @description Tìm kiếm thông tin của khách bằng những param
   */
  async searchPerson(payload: SearchPersonDto, configs?: HttpClientRequestConfig): Promise<SearchPersonRes> {
    const axiosRes = await this.httpClientService.get(_interpolateString(FamilyServiceApiEndpoint.SEARCH_PERSON), {
      ...configs,
      params: payload,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(SearchPersonRes, axiosRes?.data || null);
  }

  /**
   * @description Lấy thông tin khách hàng qua LcvId
   */
  async getPersonByLcvId(lcvId: string, configs?: HttpClientRequestConfig): Promise<GetPersonByIdRes> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(FamilyServiceApiEndpoint.GET_PERSON_BY_LCV_ID, lcvId),
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(GetPersonByIdRes, axiosRes?.data || null);
  }

  /**
   * @description Lấy thông tin khách hàng qua LcvId_v2
   */
  async getPersonByLcvIdV2(
    lcvId: string,
    payload?: GetDetailFamilyDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetPersonByIdRes> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(FamilyServiceApiEndpoint.GET_PERSON_BY_LCV_ID_V2, lcvId),
      {
        ...configs,
        params: payload,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(GetPersonByIdRes, axiosRes?.data || null);
  }

  async getByNationalCode(
    nationalVaccineCode: string,
    configs?: HttpClientRequestConfig,
  ): Promise<GetPersonByNationalCodeRes> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(FamilyServiceApiEndpoint.GET_BY_NATIONAL_CODE, nationalVaccineCode),
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async updateNationalVaccineCode(
    payload: UpdateNationalVaccineCodeDto,
    configs?: HttpClientRequestConfig,
  ): Promise<Person> {
    const axiosRes = await this.httpClientService.put(
      _interpolateString(FamilyServiceApiEndpoint.PERSON_UPDATE_NATIONAL_VACCINE_CODE),
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * @description xác nhận sdt
   */
  async verifyByPhone(payload: VerifyByPhoneDto, configs?: HttpClientRequestConfig): Promise<VerifyByPhoneRes> {
    const axiosRes = await this.httpClientService.get(_interpolateString(FamilyServiceApiEndpoint.VERIFY_PHONE), {
      ...configs,
      params: payload,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(VerifyByPhoneRes, axiosRes?.data || null);
  }

  async getManyByLcvId(
    payload: PayloadGetManyLcvIdDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetPersonByIdRes[]> {
    const axiosRes = await this.httpClientService.get(_interpolateString(FamilyServiceApiEndpoint.GET_MANY_BY_LCV_ID), {
      ...configs,
      params: payload,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async getManyByLcvIdV2(payload: string[], configs?: HttpClientRequestConfig): Promise<GetPersonByIdV2Res[]> {
    const axiosRes = await this.httpClientService.post(
      _interpolateString(FamilyServiceApiEndpoint.GET_MANY_BY_LCV_ID_V2),
      payload,
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(GetPersonByIdV2Res, axiosRes?.data || null);
  }

  async updatePerson(payload: UpdatePersonDto, configs?: HttpClientRequestConfig): Promise<UpdatePersonRes> {
    const axiosRes = await this.httpClientService.put(
      _interpolateString(FamilyServiceApiEndpoint.UPDATE_PERSON),
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(UpdatePersonRes, axiosRes?.data || null);
  }

  async searchPrimaryPersonExactly(
    payload: SearchPrimaryPersonExactlyDto,
    configs?: HttpClientRequestConfig,
  ): Promise<any> {
    const axiosRes = await this.httpClientService.post(
      _interpolateString(FamilyServiceApiEndpoint.SEARCH_PRIMARY_PERSON_EXACTLY),
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * @description Lấy thông tin khách hàng bằng ID
   */
  async getPersonById(id: string, configs?: HttpClientRequestConfig): Promise<GetPersonByIdRes> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(FamilyServiceApiEndpoint.GET_PERSON_BY_ID, id),
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(GetPersonByIdRes, axiosRes?.data || null);
  }

  async updatePhoneNumber(
    payload: UpdatePhoneNumberDto,
    configs?: HttpClientRequestConfig,
  ): Promise<VerifyPhoneNumberRes> {
    const axiosRes = await this.httpClientService.put(
      _interpolateString(FamilyServiceApiEndpoint.UPDATE_PHONE_NUMBER),
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async deletePhoneNumber(
    payload: DeletePhoneNumberDto[],
    configs?: HttpClientRequestConfig,
  ): Promise<VerifyPhoneNumberRes> {
    const axiosRes = await this.httpClientService.put(
      _interpolateString(FamilyServiceApiEndpoint.DELETE_PHONE_NUMBER),
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async inactivePerson(payload: InactivePersonDto, configs?: HttpClientRequestConfig): Promise<any> {
    const axiosRes = await this.httpClientService.put(
      _interpolateString(FamilyServiceApiEndpoint.ACTIVE_PERSON),
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async getFamilyById(
    personId: string,
    filters?: GetDetailFamilyDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetPersonByIdRes> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(FamilyServiceApiEndpoint.GET_PERSON_BY_ID, personId),
      {
        ...configs,
        params: filters,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * @description Tạo thông tin mang thai
   */
  async getFamilyForReportSymptom(payload: string[], configs?: HttpClientRequestConfig): Promise<GetPersonByIdRes[]> {
    const axiosRes = await this.httpClientService.post(
      _interpolateString(FamilyServiceApiEndpoint.GET_PERSON_AND_HOT_BY_LCVID),
      payload,
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async searchPrimaryPersonByKeyWord(keyword: string, configs?: HttpClientRequestConfig): Promise<any> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(FamilyServiceApiEndpoint.SEARCH_PRIMARY_PERSON_BY_KEY_WORD),
      {
        ...configs,
        params: {
          keyword,
        },
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * @TODO tìm kiếm khách hàng với family và tcqg
   */
  async searchPersonFamily(
    payload: SearchPersonDto,
    configs?: HttpClientRequestConfig,
  ): Promise<SearchPersonFamilyRes> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(FamilyServiceApiEndpoint.SEARCH_PERSON_FAMILY),
      {
        ...configs,
        params: payload,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(SearchPersonFamilyRes, axiosRes?.data || null);
  }

  /**
   * @TODO lấy thông tin khách hàng bởi customerId
   */
  async getPersonByCustIdV2(customerId: string, configs?: HttpClientRequestConfig): Promise<ListPersonByCustIdRes> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(FamilyServiceApiEndpoint.GET_PERSON_BY_CUST_ID, customerId),
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async updateAddress(payload: UpdateAddressDto, configs?: HttpClientRequestConfig): Promise<Person> {
    const axiosRes = await this.httpClientService.put(
      _interpolateString(FamilyServiceApiEndpoint.PERSON_UPDATE_ADDRESS),
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  /**
   * @TODO cập nhật person và trả về person
   */
  async updateOnlyPerson(payload: UpdatePersonDto, configs?: HttpClientRequestConfig): Promise<UpdatePersonRes> {
    const axiosRes = await this.httpClientService.put(
      _interpolateString(FamilyServiceApiEndpoint.UPDATE_PERSON_V2),
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(UpdatePersonRes, axiosRes?.data || null);
  }

  async addFamilyMember(payload: AddFamilyMemberDto, configs?: HttpClientRequestConfig): Promise<any> {
    const axiosRes = await this.httpClientService.put(
      _interpolateString(FamilyServiceApiEndpoint.ADD_FAMILY_MEMBER),
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async updateFamilyMember(payload: UpdateFamilyMemberDto, configs?: HttpClientRequestConfig): Promise<any> {
    const axiosRes = await this.httpClientService.put(
      _interpolateString(FamilyServiceApiEndpoint.UPDATE_FAMILY_MEMBER),
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async searchFamilyByKeyWord(
    payload: SearchFamilyByKeywordDto,
    configs?: HttpClientRequestConfig,
  ): Promise<SearchFamilyByKeywordResponseDto[]> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(FamilyServiceApiEndpoint.SEARCH_FAMILY_BY_KEY_WORD),
      {
        ...configs,
        params: payload,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async changeFamilyProfile(
    payload: ChangeFamilyProfileDto,
    configs?: HttpClientRequestConfig,
  ): Promise<ChangeFamilyProfileResponseDto> {
    const axiosRes = await this.httpClientService.put(
      _interpolateString(FamilyServiceApiEndpoint.CHANGE_FAMILY_PROFILE),
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async getListTitle(configs?: HttpClientRequestConfig): Promise<GetListTitleDto[]> {
    const axiosRes = await this.httpClientService.get(_interpolateString(FamilyServiceApiEndpoint.GET_LIST_TITLE), {
      ...configs,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(GetListTitleDto, axiosRes?.data || null);
  }

  async deleteFamilyProfile(payload: DeleteFamilyProfileDto, configs?: HttpClientRequestConfig): Promise<Person> {
    const axiosRes = await this.httpClientService.delete(
      _interpolateString(FamilyServiceApiEndpoint.DELETE_FAMILY_PROFILE),
      {
        ...configs,
        data: payload,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async inactivePersonContactByPersonId(
    payload: DeletePersonContractDto,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean> {
    const axiosRes = await this.httpClientService.delete(
      _interpolateString(FamilyServiceApiEndpoint.INACTIVE_PERSON_CONTACT_BY_PERSON_ID),
      {
        ...configs,
        data: {
          ...payload,
        },
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async updateHostForPerson(payload: ChangeHostForPersonDto, configs?: HttpClientRequestConfig): Promise<Person> {
    const axiosRes = await this.httpClientService.put(
      _interpolateString(FamilyServiceApiEndpoint.CHANGE_HOST_FOR_PERSON),
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async getPrimaryShortPerson(lcvId: string[], configs?: HttpClientRequestConfig): Promise<GetPrimaryShortPersonDto[]> {
    const axiosRes = await this.httpClientService.post(
      _interpolateString(FamilyServiceApiEndpoint.GET_PRIMARI_SHORT_PERSON),
      lcvId,
      { ...configs },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(GetPrimaryShortPersonDto, axiosRes?.data?.items || null);
  }

  async getSinglePrimaryPerson(payload: string, configs?: HttpClientRequestConfig): Promise<GetPersonByIdV2Res> {
    const axiosRes = await this.httpClientService.post(
      _interpolateString(FamilyServiceApiEndpoint.GET_LIST_PRIMARY_PERSON),
      payload,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return _transformPlainToInstance(Person, axiosRes?.data || null);
  }
}
