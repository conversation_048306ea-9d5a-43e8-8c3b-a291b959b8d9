import { HttpClientRequestConfig } from '../../http-client/src';
import {
  CreatePersonDto,
  CreatePersonRes,
  GetPersonByIdRes,
  GetPersonByIdV2Res,
  GetPersonByNationalCodeRes,
  PayloadGetManyLcvIdDto,
  Person,
  ReqGetFamilyByLcvIdDto,
  ReqUpdatePersonByLcvIdDto,
  SearchPersonDto,
  SearchPersonRes,
  UpdateNationalVaccineCodeDto,
  VerifyByPhoneDto,
  VerifyByPhoneRes,
  GetDetailFamilyDto,
  SearchPrimaryPersonExactlyDto,
  DeletePhoneNumberDto,
  UpdatePhoneNumberDto,
  VerifyPhoneNumberRes,
  InactivePersonDto,
  ListPersonByCustIdRes,
  UpdateAddressDto,
  AddFamilyMemberDto,
  UpdateFamilyMemberDto,
  SearchFamilyByKeywordDto,
  SearchFamilyByKeywordResponseDto,
  ChangeFamilyProfileDto,
  ChangeFamilyProfileResponseDto,
  GetListTitleDto,
  DeleteFamilyProfileDto,
  DeletePersonContractDto,
  ChangeHostForPersonDto,
  GetPrimaryShortPersonDto,
} from './dto';
import { ReqPersonContactDto, ResPersonContactDto } from './dto/person-contact.dto';
import { UpdatePersonDto, UpdatePersonRes } from './dto/update-person.dto copy';

export abstract class FamilyServiceApiAbstract {
  abstract updatePersonByLcvId(payload: ReqUpdatePersonByLcvIdDto, configs?: HttpClientRequestConfig): Promise<Person>;
  abstract getFamilyByLcvId(payload: ReqGetFamilyByLcvIdDto, configs?: HttpClientRequestConfig): Promise<Person>;
  abstract getFamilyByLcvIdV2(payload: ReqGetFamilyByLcvIdDto, configs?: HttpClientRequestConfig): Promise<Person>;
  abstract getPrimaryPerson(payload: string[], configs?: HttpClientRequestConfig): Promise<Person[]>;
  abstract getPrimaryPersonByPhone(phones: string[], configs?: HttpClientRequestConfig): Promise<Person[]>;
  abstract getPrimaryPersonByCoreCustId(coreCustId: string, configs?: HttpClientRequestConfig): Promise<Person[]>;
  abstract getPersonContactByCoreCustId(
    payload: ReqPersonContactDto,
    configs?: HttpClientRequestConfig,
  ): Promise<ResPersonContactDto>;
  abstract createPerson(payload: CreatePersonDto, configs?: HttpClientRequestConfig): Promise<CreatePersonRes>;
  abstract getPersonByPhone(phoneNumber: string, configs?: HttpClientRequestConfig): Promise<Person[]>;
  abstract searchPerson(payload: SearchPersonDto, configs?: HttpClientRequestConfig): Promise<SearchPersonRes>;
  abstract getPersonByLcvId(lcvId: string, configs?: HttpClientRequestConfig): Promise<GetPersonByIdRes>;
  abstract getPersonByLcvIdV2(
    lcvId: string,
    payload?: GetDetailFamilyDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetPersonByIdRes>;
  abstract getByNationalCode(
    nationalVaccineCode: string,
    configs?: HttpClientRequestConfig,
  ): Promise<GetPersonByNationalCodeRes>;
  abstract updateNationalVaccineCode(
    payload: UpdateNationalVaccineCodeDto,
    configs?: HttpClientRequestConfig,
  ): Promise<Person>;
  abstract verifyByPhone(payload: VerifyByPhoneDto, configs?: HttpClientRequestConfig): Promise<VerifyByPhoneRes>;
  abstract getManyByLcvId(
    payload: PayloadGetManyLcvIdDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetPersonByIdRes[]>;
  abstract getManyByLcvIdV2(payload: string[], configs?: HttpClientRequestConfig): Promise<GetPersonByIdV2Res[]>;
  abstract updatePerson(payload: UpdatePersonDto, configs?: HttpClientRequestConfig): Promise<UpdatePersonRes>;
  abstract searchPrimaryPersonExactly(
    payload: SearchPrimaryPersonExactlyDto,
    configs?: HttpClientRequestConfig,
  ): Promise<any>;
  abstract getPersonById(id: string, configs?: HttpClientRequestConfig): Promise<GetPersonByIdRes>;
  abstract updatePhoneNumber(
    payload: UpdatePhoneNumberDto,
    configs?: HttpClientRequestConfig,
  ): Promise<VerifyPhoneNumberRes>;
  abstract deletePhoneNumber(
    payload: DeletePhoneNumberDto[],
    configs?: HttpClientRequestConfig,
  ): Promise<VerifyPhoneNumberRes>;
  abstract inactivePerson(payload: InactivePersonDto, configs?: HttpClientRequestConfig): Promise<any>;
  abstract getFamilyById(
    personId: string,
    filters?: GetDetailFamilyDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetPersonByIdRes>;
  abstract getFamilyForReportSymptom(payload: string[], configs?: HttpClientRequestConfig): Promise<GetPersonByIdRes[]>;
  abstract searchPrimaryPersonByKeyWord(keyword: string, configs?: HttpClientRequestConfig): Promise<any>;
  abstract getPersonByCustIdV2(customerId: string, configs?: HttpClientRequestConfig): Promise<ListPersonByCustIdRes>;
  abstract updateAddress(payload: UpdateAddressDto, configs?: HttpClientRequestConfig): Promise<Person>;
  abstract updateOnlyPerson(payload: UpdatePersonDto, configs?: HttpClientRequestConfig): Promise<UpdatePersonRes>;
  abstract addFamilyMember(payload: AddFamilyMemberDto, configs?: HttpClientRequestConfig): Promise<any>;
  abstract updateFamilyMember(payload: UpdateFamilyMemberDto, configs?: HttpClientRequestConfig): Promise<any>;
  abstract searchFamilyByKeyWord(
    payload: SearchFamilyByKeywordDto,
    configs?: HttpClientRequestConfig,
  ): Promise<SearchFamilyByKeywordResponseDto[]>;
  abstract changeFamilyProfile(
    payload: ChangeFamilyProfileDto,
    configs?: HttpClientRequestConfig,
  ): Promise<ChangeFamilyProfileResponseDto>;
  abstract getListTitle(configs?: HttpClientRequestConfig): Promise<GetListTitleDto[]>;
  abstract deleteFamilyProfile(payload: DeleteFamilyProfileDto, configs?: HttpClientRequestConfig): Promise<Person>;
  abstract inactivePersonContactByPersonId(
    payload: DeletePersonContractDto,
    configs?: HttpClientRequestConfig,
  ): Promise<boolean>;
  abstract updateHostForPerson(payload: ChangeHostForPersonDto, configs?: HttpClientRequestConfig): Promise<Person>;
  abstract getPrimaryShortPerson(
    lcvIds: string[],
    configs?: HttpClientRequestConfig,
  ): Promise<GetPrimaryShortPersonDto[]>;
  abstract getSinglePrimaryPerson(payload: string, configs?: HttpClientRequestConfig): Promise<GetPersonByIdV2Res>;
}
