import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';
import { AttributeShop } from '../../../cart-app-customer-api/src/dto';
import { SellPriority } from '../../../pim-product-core-api/src/dto';
import { Measure, Price } from '../../../regimen-app-api';

export class SearchCatalogDto {
  @ApiProperty({ description: 'Search keyword', required: false })
  @Expose()
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({ description: 'Date of birth for age-based filtering', required: false })
  @Expose()
  @IsOptional()
  dob?: Date;

  @ApiProperty({ description: 'Gender (0: female, 1: male)', required: false })
  @Expose()
  @IsOptional()
  @IsNumber()
  gender?: number;

  @ApiProperty({ description: 'Long Chau customer ID', required: false })
  @Expose()
  @IsOptional()
  @IsString()
  lcvId?: string;

  @ApiProperty({ description: 'Vaccine national code from TCQG', required: false })
  @Expose()
  @IsOptional()
  @IsString()
  vaccineNationalCode?: string;

  @ApiProperty({ description: 'Page number for pagination', required: false, default: 1 })
  @Expose()
  @IsOptional()
  @IsNumber()
  page?: number = 1;

  @ApiProperty({ description: 'Page size for pagination', required: false, default: 20 })
  @Expose()
  @IsOptional()
  @IsNumber()
  size?: number = 20;
}

export class InjectionInfoDto {
  @ApiProperty()
  @Expose()
  injected: number;

  @ApiProperty()
  @Expose()
  sku: string;

  @ApiProperty({ required: false })
  @Expose()
  lastInjectedVaccineName?: string;
}

export class DetailDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @Expose()
  id?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @Expose()
  regimenId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Expose()
  order?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @Expose()
  nearestInjectionDistance?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Expose()
  nearestInjectionDistanceValue?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Expose()
  distanceValueByType?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  @Expose()
  isRequired?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Expose()
  dosage?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @Expose()
  unit?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Expose()
  minDistance?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Expose()
  maxDistance?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Expose()
  dayAllowEarly?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Expose()
  distanceType?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  @Expose()
  isActive?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Expose()
  ageFromUnitCode?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Expose()
  ageFromValue?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Expose()
  ageToUnitCode?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Expose()
  ageToValue?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  @Expose()
  equalFromAge?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  @Expose()
  equalToAge?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @Expose()
  repeatFrequency?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  @Expose()
  isRepeatDose?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @Expose()
  distance?: string;
}

export class VaccineWithInjectionInfoDto {
  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  sku: string;

  @ApiProperty()
  @Expose()
  manufactor: string;

  @ApiProperty()
  @Expose()
  injectedCount?: number;

  @ApiProperty()
  @Expose()
  lastInjectedDate?: Date;

  @ApiProperty({
    type: SellPriority,
  })
  @Type(() => SellPriority)
  @Expose()
  sellPriority: SellPriority;

  @ApiProperty({
    type: Price,
    isArray: true,
  })
  @Type(() => Price)
  @Expose()
  prices: Price[];

  @ApiProperty({
    type: Measure,
    isArray: true,
  })
  @Type(() => Measure)
  @Expose()
  measures: Measure[];

  @ApiProperty({
    type: DetailDto,
    isArray: true,
  })
  @Expose()
  @Type(() => DetailDto)
  details: DetailDto[];

  @ApiProperty()
  @Expose()
  regimenId?: string;

  @ApiProperty()
  @Expose()
  scheduleType?: string;

  @ApiProperty()
  @Expose()
  isAgeEligible?: boolean;

  @ApiProperty()
  @Expose()
  @Type(() => AttributeShop)
  attributeShop?: AttributeShop;

  @ApiProperty()
  @Expose()
  isPreOrder?: boolean;

  @ApiProperty()
  @Expose()
  isMultiDose?: boolean;

  @ApiProperty()
  @Expose()
  requiredInjections?: number;
}

export class DiseaseGroupDto {
  @ApiProperty()
  @Expose()
  disease: string;

  @ApiProperty()
  @Expose()
  engName: string;

  @ApiProperty()
  @Expose()
  normalizeName: string;

  @ApiProperty()
  @Expose()
  viName: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  maxInjections: number;
}

export class CatalogSearchRes {
  @ApiProperty()
  @Expose()
  diseaseGroupId: string;

  @ApiProperty({ type: DiseaseGroupDto })
  @Expose()
  @Type(() => DiseaseGroupDto)
  diseaseGroup: DiseaseGroupDto;

  @ApiProperty()
  @Expose()
  isRecommended?: boolean;

  @ApiProperty()
  @Expose()
  hasAdviceScript?: boolean;

  @ApiProperty({ type: [VaccineWithInjectionInfoDto] })
  @Expose()
  @Type(() => VaccineWithInjectionInfoDto)
  vaccines: VaccineWithInjectionInfoDto[];

  @ApiProperty({ type: [InjectionInfoDto] })
  @Expose()
  @Type(() => InjectionInfoDto)
  injectionInfo: InjectionInfoDto;
}

export class SearchVaccinesRes {
  @ApiProperty({ type: [CatalogSearchRes] })
  @Expose()
  topDiseaseGroups: CatalogSearchRes[];

  @ApiProperty({ type: [CatalogSearchRes] })
  @Expose()
  remainingDiseaseGroups: CatalogSearchRes[];
}
