import { Injectable } from '@nestjs/common';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import { VoucherAppApiAbstract } from './voucher-app-api.abstract';
import {
  CustomerVoucherAllReqDto,
  CustomerVoucherAllResDto,
  CustomerVoucherClaimReqDto,
  CustomerVoucherClaimResDto,
  CustomerVoucherCreateKeyReqDto,
  CustomerVoucherCreateKeyResDto,
  CustomerVoucherGetVoucherByIdReqDto,
  CustomerVoucherGetVoucherByIdResDto,
  CustomerVoucherGetVoucherInfoByPhoneReqDto,
  CustomerVoucherGetVoucherInfoByPhoneResDto,
} from './dto';
import { VOUCHER_APP_API_URL } from './voucher-app-api.endpoint';
import { _checkExceptionFromAxiosRes, _interpolateString } from '../../functions';

@Injectable()
export class VoucherAppApiService extends VoucherAppApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }
  async getAll(
    payload: CustomerVoucherAllReqDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CustomerVoucherAllResDto> {
    const axiosRes = await this.httpClientService.get(_interpolateString(VOUCHER_APP_API_URL.CUSTOMER_VOUCHER_ALL), {
      ...configs,
      params: payload,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }
  async claim(
    payload: CustomerVoucherClaimReqDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CustomerVoucherClaimResDto> {
    const axiosRes = await this.httpClientService.post(VOUCHER_APP_API_URL.CUSTOMER_VOUCHER_CLAIM, payload, configs);
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }
  async createKey(
    payload: CustomerVoucherCreateKeyReqDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CustomerVoucherCreateKeyResDto> {
    const axiosRes = await this.httpClientService.post(
      VOUCHER_APP_API_URL.CUSTOMER_VOUCHER_CREATE_KEY,
      payload,
      configs,
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  async getVouchersByPhone(
    payload: CustomerVoucherGetVoucherInfoByPhoneReqDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CustomerVoucherGetVoucherInfoByPhoneResDto> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(VOUCHER_APP_API_URL.CUSTOMER_VOUCHER_GET_VOUCHER_INFO_BY_PHONE),
      {
        ...configs,
        params: payload,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  async getVoucherById(
    payload: CustomerVoucherGetVoucherByIdReqDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CustomerVoucherGetVoucherByIdResDto> {
    const axiosRes = await this.httpClientService.get(
      _interpolateString(VOUCHER_APP_API_URL.CUSTOMER_VOUCHER_GET_VOUCHER_BY_ID),
      {
        ...configs,
        params: payload,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }
}
