import { IsString } from 'class-validator';

export class CustomerVoucherAllReqDto {}

export class CustomerVoucherAllResDto {}

export class CustomerVoucherClaimReqDto {}

export class CustomerVoucherClaimResDto {}

export class CustomerVoucherCreateKeyReqDto {}

export class CustomerVoucherCreateKeyResDto {}

export class CustomerVoucherGetVoucherInfoByPhoneReqDto {
  @IsString()
  phone: string;
}

export class CustomerVoucherGetVoucherInfoByPhoneResDto {}

export class CustomerVoucherGetVoucherByIdReqDto {
  @IsString()
  voucherId: string;

  @IsString()
  phone: string;
}

export class CustomerVoucherGetVoucherByIdResDto {}
