import { HttpClientRequestConfig } from '../../http-client/src';
import {
  CustomerVoucherAllReqDto,
  CustomerVoucherAllResDto,
  CustomerVoucherClaimReqDto,
  CustomerVoucherClaimResDto,
  CustomerVoucherCreateKeyReqDto,
  CustomerVoucherCreateKeyResDto,
  CustomerVoucherGetVoucherByIdReqDto,
  CustomerVoucherGetVoucherByIdResDto,
  CustomerVoucherGetVoucherInfoByPhoneReqDto,
  CustomerVoucherGetVoucherInfoByPhoneResDto,
} from './dto';

export abstract class VoucherAppApiAbstract {
  abstract getAll(
    payload: CustomerVoucherAllReqDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CustomerVoucherAllResDto>;

  abstract claim(
    payload: CustomerVoucherClaimReqDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CustomerVoucherClaimResDto>;

  abstract createKey(
    payload: CustomerVoucherCreateKeyReqDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CustomerVoucherCreateKeyResDto>;

  abstract getVouchersByPhone(
    payload: CustomerVoucherGetVoucherInfoByPhoneReqDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CustomerVoucherGetVoucherInfoByPhoneResDto>;

  abstract getVoucherById(
    payload: CustomerVoucherGetVoucherByIdReqDto,
    configs?: HttpClientRequestConfig,
  ): Promise<CustomerVoucherGetVoucherByIdResDto>;
}
