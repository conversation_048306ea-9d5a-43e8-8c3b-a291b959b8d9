import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class GetCustomerTagReqDto {
  @ApiProperty()
  @Expose()
  phoneNumber?: string;

  @ApiProperty()
  @Expose()
  email?: string;

  @ApiProperty()
  @Expose()
  vaccinationCode?: string;
}

export class CreateCustomerTagReqDto {
  @ApiProperty()
  @Expose()
  phoneNumber?: string;

  @ApiProperty()
  @Expose()
  email?: string;

  @ApiProperty()
  @Expose()
  vaccinationCode?: string;

  @ApiProperty()
  @Expose()
  tagId?: string;
}
