import { Injectable } from '@nestjs/common';
import { _checkExceptionFromAxiosRes } from '../../functions';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import { CarsApiAbstract } from './cars-service-api.abstract';
import { CARS_API_URL } from './cars-service-api.endpoint';
import { CreateCustomerTagReqDto, GetCustomerTagReqDto } from './dto';
import { GetCustomerTagResponse } from './dto/cars.dto';

@Injectable()
export class CarsApiService extends CarsApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  async getCustomerTag(
    payload: GetCustomerTagReqDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetCustomerTagResponse[]> {
    const axiosRes = await this.httpClientService.get(CARS_API_URL.CUSTOMER_TAG, {
      ...configs,
      params: payload,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }

  async createCustomerTag(
    payload: CreateCustomerTagReqDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetCustomerTagResponse[]> {
    const axiosRes = await this.httpClientService.post(CARS_API_URL.CUSTOMER_TAG, payload, {
      ...configs,
    });
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data || null;
  }
}
