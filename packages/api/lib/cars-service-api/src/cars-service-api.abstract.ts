import { HttpClientRequestConfig } from '../../http-client/src';
import { CreateCustomerTagReqDto, GetCustomerTagReqDto } from './dto';
import { GetCustomerTagResponse } from './dto/cars.dto';

export abstract class CarsApiAbstract {
  abstract getCustomerTag(
    payload: GetCustomerTagReqDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetCustomerTagResponse[]>;

  abstract createCustomerTag(
    payload: CreateCustomerTagReqDto,
    configs?: HttpClientRequestConfig,
  ): Promise<GetCustomerTagResponse[]>;
}
