import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsString } from 'class-validator';

export class SendChatDto {
  @Expose()
  @ApiProperty()
  message: string;

  @Expose()
  @ApiProperty()
  @IsString()
  groupId: string;
}

export class SendChatResponseDto {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  status: string;

  @Expose()
  @ApiProperty()
  created_at: string;

  @Expose()
  @ApiProperty()
  message: string;
}
