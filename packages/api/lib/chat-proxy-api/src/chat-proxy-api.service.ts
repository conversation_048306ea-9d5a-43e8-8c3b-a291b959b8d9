import { Injectable } from '@nestjs/common';
import { _checkExceptionFromAxiosRes, _transformPlainToInstance } from '../../functions';
import { HttpClientRequestConfig, HttpClientService } from '../../http-client/src';
import { SendChatDto, SendChatResponseDto } from './dto';
import { ChatProxyApiAbstract } from './chat-proxy-api.abstract';
import { ChatProxyApiEndpoint } from './chat-proxy-api.endpoint';

/**
 * VAC Approval API Service
 * Implements approval ticket management operations
 */
@Injectable()
export class ProxyChatApiService extends ChatProxyApiAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  async sendChat(request: SendChatDto, configs?: HttpClientRequestConfig): Promise<SendChatResponseDto> {
    const axiosRes = await this.httpClientService.post(
      `${ChatProxyApiEndpoint.SEND_CHAT}/${request?.groupId}`,
      request,
      {
        ...configs,
      },
    );
    _checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }
}
