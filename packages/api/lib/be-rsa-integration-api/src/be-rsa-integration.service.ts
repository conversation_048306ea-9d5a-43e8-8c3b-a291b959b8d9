import { HttpStatus, Injectable } from '@nestjs/common';
import { ClassConstructor, plainToInstance } from 'class-transformer';
import { HttpClientRequestConfig, HttpClientResponse, HttpClientService } from '../../http-client/src';
import { CoreException } from './exceptions/core.exception';
import { IErrorCoreResponse } from './interfaces';
import { BeRSAIntegrationAbstract } from './be-rsa-integration.abstract';
import { BeRSAIntegrationEndpoint } from './be-rsa-integration.endpoint';
import {
  ClearMisscallDto,
  GetMissCallDto,
  GetRecordBySessionReq,
  GetRecordReq,
  LogEventReq,
  LoginDto,
} from '../../telco-api';

@Injectable()
export class BeRSAIntegrationService extends BeRSAIntegrationAbstract {
  constructor(private readonly httpClientService: HttpClientService) {
    super();
  }

  protected _transformPlainToInstance<T>(obj: ClassConstructor<T>, rawData: T | T[]): T | T[] {
    if (Array.isArray(rawData)) {
      return rawData.map((e) =>
        plainToInstance(obj, e, {
          excludeExtraneousValues: true,
          exposeUnsetFields: false,
        }),
      );
    }
    return plainToInstance(obj, rawData, {
      excludeExtraneousValues: true,
      exposeUnsetFields: false,
    });
  }

  protected _checkExceptionFromAxiosRes(axiosRes: HttpClientResponse) {
    if (!axiosRes) return null;
    if (
      axiosRes.status !== HttpStatus.OK &&
      axiosRes.status !== HttpStatus.NO_CONTENT &&
      axiosRes.status !== HttpStatus.CREATED
    ) {
      const error = axiosRes?.data?.error || axiosRes?.data;
      error.code = axiosRes?.data?.error?.code || axiosRes?.data?.errorCode || 'CORE_ERROR:00500';
      error.validationErrors = axiosRes?.data?.error?.validationErrors || axiosRes?.data?.errors;
      throw new CoreException({ error } as unknown as IErrorCoreResponse, axiosRes.status, axiosRes.config);
    }
  }

  protected _interpolateString(template: string, ...values: any[]) {
    return template.replace(/{(\d+)}/g, (_match, index) => {
      const value = values[index] || '';
      return value;
    });
  }

  async login(payload: LoginDto, configs?: HttpClientRequestConfig): Promise<any> {
    const axiosRes = await this.httpClientService.post(
      this._interpolateString(BeRSAIntegrationEndpoint.LOGIN),
      payload,
      configs,
    );
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async getRecord(payload: GetRecordReq, configs?: HttpClientRequestConfig): Promise<any> {
    const axiosRes = await this.httpClientService.post(
      this._interpolateString(BeRSAIntegrationEndpoint.RECORDS),
      payload,
      configs,
    );
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async getRecordBySessions(payload: GetRecordBySessionReq, configs?: HttpClientRequestConfig): Promise<any> {
    const axiosRes = await this.httpClientService.post(
      this._interpolateString(BeRSAIntegrationEndpoint.RECORDS_BY_SESSIONS),
      payload,
      configs,
    );
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async getMisscall(payload?: GetMissCallDto, configs?: HttpClientRequestConfig): Promise<any> {
    const axiosRes = await this.httpClientService.get(this._interpolateString(BeRSAIntegrationEndpoint.GET_MISS_CALL), {
      params: payload,
      ...configs,
    });
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async clearMisscall(payload?: ClearMisscallDto, configs?: HttpClientRequestConfig): Promise<any> {
    const axiosRes = await this.httpClientService.delete(
      this._interpolateString(BeRSAIntegrationEndpoint.CLEAR_MISS_CALL),
      {
        ...configs,
        data: payload,
        params: { tenant: payload?.tenant },
      },
    );
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }

  async saveLogEventCall(payload: LogEventReq, configs?: HttpClientRequestConfig): Promise<any> {
    const axiosRes = await this.httpClientService.post(
      this._interpolateString(BeRSAIntegrationEndpoint.PUSH_LOG_EVENT),
      payload,
      configs,
    );
    this._checkExceptionFromAxiosRes(axiosRes);
    return axiosRes?.data;
  }
}
