import { HttpException } from '@nestjs/common';
import { HttpClientResponse } from '../../../http-client/src';
import { ErrorCode } from '../errors/error-code';
import { IErrorCoreResponse } from '../interfaces';

export class CoreException extends HttpException {
  constructor(exceptionResponse: IErrorCoreResponse, statusCode: number, config: HttpClientResponse['config']) {
    super(
      HttpException.createBody({
        code: exceptionResponse?.error?.code || ErrorCode.CORE_INTERNAL_SERVER,
        message: exceptionResponse?.error?.message || ErrorCode.getError(ErrorCode.CORE_INTERNAL_SERVER),
        details: exceptionResponse?.error?.details || ErrorCode.getError(ErrorCode.CORE_INTERNAL_SERVER),
        validationErrors: exceptionResponse?.error?.validationErrors || null,
        config: config,
      }),
      statusCode,
    );
  }
}
