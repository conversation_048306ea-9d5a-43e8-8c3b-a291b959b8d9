import { HttpClientRequestConfig } from '../../http-client/src';
import {
  ClearMisscallDto,
  GetMissCallDto,
  GetRecordBySessionReq,
  GetRecordReq,
  LogEventReq,
  LoginDto,
  RecordBySessionRes,
  RecordRes,
} from '../../telco-api';

export abstract class BeRSAIntegrationAbstract {
  abstract login(payload: LoginDto, configs?: HttpClientRequestConfig): Promise<any>;

  abstract getRecord(payload: GetRecordReq, configs?: HttpClientRequestConfig): Promise<any>;

  abstract getRecordBySessions(payload: GetRecordBySessionReq, configs?: HttpClientRequestConfig): Promise<any>;

  abstract getMisscall(payload?: GetMissCallDto, configs?: HttpClientRequestConfig): Promise<any>;

  abstract clearMisscall(payload?: ClearMisscallDto, configs?: HttpClientRequestConfig): Promise<any>;

  abstract saveLogEventCall(payload: LogEventReq, configs?: HttpClientRequestConfig): Promise<any>;
}
