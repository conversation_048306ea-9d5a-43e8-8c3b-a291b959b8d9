export class ErrorCode {
  static CORE_INTERNAL_SERVER = 'CORE_ERROR:00500';

  private static createErrorMap(): Map<string, string> {
    const errorCode = new Map();
    errorCode.set(this.CORE_INTERNAL_SERVER, 'Lỗi hệ thống tích hợp');
    return errorCode;
  }

  private static errorMap = ErrorCode.createErrorMap();

  static getError(code: string): string {
    if (this.errorMap.has(code)) {
      return this.errorMap.get(code);
    }
    return 'Error code has not been defined';
  }

  static defaultErrorCode() {
    return 'ERR:00000';
  }
}
