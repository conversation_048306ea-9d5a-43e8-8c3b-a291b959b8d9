import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { warningEnv } from './utils';
import { MAX_REDIRECT, TIMEOUT } from './be-rsa-integration.constant';
import { HttpClientModule } from '../../http-client/src';
import { BeRSAIntegrationService } from './be-rsa-integration.service';

@Module({
  imports: [
    HttpClientModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        if (!configService.get('RSA_INTEGRATION_API_URL')) {
          warningEnv(['RSA_INTEGRATION_API_URL']);
        } else if (!configService.get('RSA_INTEGRATION_URL')) {
          warningEnv(['RSA_INTEGRATION_URL']);
        }

        // if (!configService.get('TELCO_TENANT')) {
        //   warningEnv(['TELCO_TENANT']);
        // }

        return {
          baseURL: configService.get('RSA_INTEGRATION_API_URL') || configService.get('RSA_INTEGRATION_URL'),
          // headers: {
          //   Tenant: configService.get('TELCO_TENANT'),
          // },
          timeout: TIMEOUT,
          maxRedirects: MAX_REDIRECT,
          validateStatus: () => {
            return true;
          },
        };
      },
      inject: [ConfigService],
    }),
  ],
  providers: [BeRSAIntegrationService],
  exports: [BeRSAIntegrationService],
})
export class BeRSAIntegrationModule {}
